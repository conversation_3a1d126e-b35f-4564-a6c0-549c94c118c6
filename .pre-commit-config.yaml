# EduGuideBot 2025 - Pre-commit Hooks
# Ensures code quality and prevents common issues

repos:
  # Code formatting
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=100]

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile=black, --line-length=100]

  # Code linting
  - repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=100, --ignore=E203,W503]

  # Security checks
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, src/, -f, json]
        exclude: tests/

  # YAML validation
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.33.0
    hooks:
      - id: yamllint
        args: [-d, relaxed]

  # General file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-merge-conflict
      - id: check-added-large-files
        args: [--maxkb=1000]

  # Prevent committing secrets
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: [--baseline, .secrets.baseline]
        exclude: \.env\.example$

  # Custom hooks for project-specific rules
  - repo: local
    hooks:
      # Prevent root-level Python files (except specific ones)
      - id: no-root-python-files
        name: No root-level Python files
        entry: bash -c 'if ls *.py 2>/dev/null | grep -v -E "(demo_cli\.py|sanity_check\.py|test_week1\.py)"; then echo "❌ Root-level Python files detected! Use src/bot/app.py as canonical entry point."; exit 1; fi'
        language: system
        pass_filenames: false

      # Ensure BOT_TOKEN is not hardcoded
      - id: no-hardcoded-tokens
        name: No hardcoded bot tokens
        entry: bash -c 'if grep -r "BOT_TOKEN.*=" src/ | grep -v "getenv\|environ"; then echo "❌ Hardcoded BOT_TOKEN detected!"; exit 1; fi'
        language: system
        pass_filenames: false

      # Validate Khmer text encoding
      - id: khmer-encoding-check
        name: Khmer text encoding validation
        entry: python -c "
import sys
for file in sys.argv[1:]:
    try:
        with open(file, 'r', encoding='utf-8') as f:
            content = f.read()
        # Check for common Khmer Unicode ranges
        if any(ord(c) in range(0x1780, 0x17FF) for c in content):
            print(f'✅ {file} - Valid Khmer encoding')
    except UnicodeDecodeError:
        print(f'❌ {file} - Invalid UTF-8 encoding')
        sys.exit(1)
"
        language: python
        files: \.(py|yaml|yml|json|md)$
        require_serial: true
