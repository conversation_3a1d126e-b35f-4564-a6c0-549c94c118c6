#!/usr/bin/env python3
"""
EduGuideBot CLI Demo - Presentation Fallback
================================================================

Offline CLI demonstration of the MCDA recommendation system.
Use this if Telegram/internet fails during university presentation.

Features:
- Same MCDA engine as Telegram bot
- Interactive CLI interface in Khmer
- Complete assessment flow
- Detailed recommendation display
- No internet connection required
"""

import sys
import os
from pathlib import Path
from typing import Dict, List

# Add src directory to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

try:
    from mcda_recommender import MCDARecommendationEngine
except ImportError:
    print("❌ MCDA engine not found. Check src/mcda_recommender.py")
    sys.exit(1)

# Assessment questions (same as bot)
ASSESSMENT_QUESTIONS = {
    1: {
        "question_kh": "អ្នកចង់សិក្សានៅទីណា?",
        "key": "location",
        "options": [
            {"text": "ភ្នំពេញ", "value": "phnom_penh"},
            {"text": "សៀមរាប", "value": "siem_reap"},
            {"text": "បាត់ដំបង", "value": "battambang"},
            {"text": "អនឡាញ", "value": "online"}
        ]
    },
    2: {
        "question_kh": "ថវិកាសិក្សារបស់អ្នកប្រហែលប៉ុន្មាន?",
        "key": "budget",
        "options": [
            {"text": "ទាប (< $500/ឆ្នាំ)", "value": "low"},
            {"text": "មធ្យម ($500-1500/ឆ្នាំ)", "value": "mid"},
            {"text": "ខ្ពស់ (> $1500/ឆ្នាំ)", "value": "high"}
        ]
    },
    3: {
        "question_kh": "អ្នកចូលចិត្តរបៀបសិក្សាណាមួយ?",
        "key": "learning_mode",
        "options": [
            {"text": "ប្រពៃណី (នៅសាលា)", "value": "traditional"},
            {"text": "បញ្ចូលគ្នា", "value": "blended"},
            {"text": "អនឡាញតែម្យ៉ាង", "value": "online_only"}
        ]
    },
    4: {
        "question_kh": "អ្នកចាប់អារម្មណ៍លើផ្នែកណា?",
        "key": "interest_field",
        "options": [
            {"text": "វិទ្យាសាស្ត្រ និងបច្ចេកវិទ្យា", "value": "stem"},
            {"text": "សិល្បៈ និងការច្នៃប្រឌិត", "value": "arts"},
            {"text": "អាជីវកម្ម និងគ្រប់គ្រង", "value": "business"},
            {"text": "កសិកម្ម និងបរិស្ថាន", "value": "agro_env"},
            {"text": "សុខភាព និងវេជ្ជសាស្ត្រ", "value": "health"}
        ]
    },
    5: {
        "question_kh": "គោលដៅអាជីពរបស់អ្នកជាអ្វី?",
        "key": "career_goal",
        "options": [
            {"text": "បច្ចេកវិទ្យាឌីជីថល", "value": "digital_tech"},
            {"text": "វិស្វកម្ម", "value": "engineering"},
            {"text": "ហិរញ្ញវត្ថុ", "value": "finance"},
            {"text": "អប់រំ", "value": "education"},
            {"text": "មិនទាន់ប្រាកដ", "value": "unsure"}
        ]
    }
    # Shortened for demo - can add all 16 questions
}

class CLIDemo:
    """CLI demonstration of EduGuideBot MCDA system."""
    
    def __init__(self):
        """Initialize CLI demo."""
        self.engine = None
        self.answers = {}
        
    def initialize(self):
        """Initialize MCDA engine."""
        print("🚀 កំពុងចាប់ផ្តើម EduGuideBot CLI Demo...")
        print("=" * 60)
        
        try:
            self.engine = MCDARecommendationEngine()
            if not self.engine.is_ready:
                raise Exception("MCDA engine failed to initialize")
            
            print(f"✅ ប្រព័ន្ធ MCDA ត្រូវបានផ្ទុក: {len(self.engine.programs)} កម្មវិធីសិក្សា")
            print(f"✅ ម៉ាទ្រីសទម្ងន់: {len(self.engine.weights_matrix)} ប្រភេទ")
            return True
            
        except Exception as e:
            print(f"❌ កំហុសក្នុងការចាប់ផ្តើម: {e}")
            return False
    
    def show_welcome(self):
        """Show welcome message."""
        print("\n🎓 សូមស្វាគមន៍មកកាន់ EduGuideBot 2025!")
        print("=" * 60)
        print("📊 ប្រព័ន្ធវិភាគ MCDA (Multi-Criteria Decision Analysis)")
        print("🎯 ការណែនាំសាកលវិទ្យាល័យដោយប្រើបញ្ញាសិប្បនិម្មិត្ត")
        print("🔍 ការវិភាគពហុវិធានការដោយមានតម្លាភាព")
        print("=" * 60)
        print("\n📋 ដំណើរការ:")
        print("• សំណួរវាយតម្លៃ ១៦ ចំណុច")
        print("• ការវិភាគតាមទំហំ ៥ ផ្នែក")
        print("• ការណែនាំដែលមានហេតុផល")
        print("• ការពន្យល់ច្បាស់លាស់ជាភាសាខ្មែរ")
        print("\n" + "=" * 60)
    
    def ask_question(self, question_num: int) -> bool:
        """Ask a single assessment question."""
        if question_num not in ASSESSMENT_QUESTIONS:
            return False
        
        question = ASSESSMENT_QUESTIONS[question_num]
        
        print(f"\n📊 សំណួរ {question_num}/{len(ASSESSMENT_QUESTIONS)}")
        print("=" * 40)
        print(f"❓ {question['question_kh']}")
        print()
        
        # Show options
        for i, option in enumerate(question['options'], 1):
            print(f"  {i}. {option['text']}")
        
        # Get user input
        while True:
            try:
                choice = input(f"\nសូមជ្រើសរើស (1-{len(question['options'])}): ").strip()
                choice_num = int(choice)
                
                if 1 <= choice_num <= len(question['options']):
                    selected_option = question['options'][choice_num - 1]
                    self.answers[question['key']] = selected_option['value']
                    print(f"✅ បានជ្រើសរើស: {selected_option['text']}")
                    return True
                else:
                    print("❌ សូមជ្រើសរើសលេខពី 1 ដល់ " + str(len(question['options'])))
                    
            except ValueError:
                print("❌ សូមបញ្ចូលលេខ")
            except KeyboardInterrupt:
                print("\n\n👋 បានបញ្ចប់ការវាយតម្លៃ")
                return False
    
    def run_assessment(self):
        """Run the complete assessment."""
        print("\n🚀 ចាប់ផ្តើមការវាយតម្លៃ...")
        
        for question_num in range(1, len(ASSESSMENT_QUESTIONS) + 1):
            if not self.ask_question(question_num):
                return False
        
        return True
    
    def generate_recommendations(self):
        """Generate and display recommendations with timing."""
        print("\n🔄 កំពុងវិភាគ និងបង្កើតការណែនាំ...")
        print("⏳ សូមរង់ចាំបន្តិច...")

        import time
        start_time = time.perf_counter()

        try:
            recommendations = self.engine.get_recommendations(self.answers, limit=5)

            # Calculate computation time
            end_time = time.perf_counter()
            computation_ms = (end_time - start_time) * 1000

            print(f"⚡ ⏱️ ពេលវេលាគណនា: {computation_ms:.1f} ms")
            print(f"🚀 ល្បឿនដំណើរការ: {'ខ្ពស់' if computation_ms < 100 else 'មធ្យម' if computation_ms < 500 else 'យឺត'}")

            if not recommendations:
                print("😔 សូមទោស! មិនអាចស្វែងរកការណែនាំបានទេ។")
                return

            self.display_recommendations(recommendations)

        except Exception as e:
            end_time = time.perf_counter()
            computation_ms = (end_time - start_time) * 1000
            print(f"❌ កំហុសក្នុងការបង្កើតការណែនាំ (បន្ទាប់ពី {computation_ms:.1f} ms): {e}")
    
    def display_recommendations(self, recommendations):
        """Display recommendations in a formatted way."""
        print("\n🎯 ការណែនាំសាកលវិទ្យាល័យសម្រាប់អ្នក")
        print("=" * 60)
        print(f"📊 រកឃើញ {len(recommendations)} ជម្រើសល្អបំផុត:\n")
        
        for i, rec in enumerate(recommendations, 1):
            print(f"🏆 ការណែនាំទី {i}")
            print("-" * 30)
            print(f"🎓 សាកលវិទ្យាល័យ: {rec.program.university_name}")
            print(f"📚 ជំនាញ: {rec.program.major_name}")
            print(f"📍 ទីតាំង: {rec.program.campus_city}")
            print(f"🎯 ពិន្ទុផ្គូផ្គង: {rec.match_percentage:.1f}%")
            print(f"💡 ហេតុផលណែនាំ: {rec.explanation_kh}")
            
            # Show score breakdown
            print(f"📈 ការវិភាគពិន្ទុ:")
            for category, score in rec.score_breakdown.items():
                if score > 0:
                    print(f"   • {category}: +{score:.1f}")
            
            print()
        
        # Show summary
        print("=" * 60)
        print("📋 សេចក្តីសង្ខេប:")
        print(f"• ការណែនាំសរុប: {len(recommendations)}")
        print(f"• ការណែនាំល្អបំផុត: {recommendations[0].program.university_name}")
        print(f"• ពិន្ទុខ្ពស់បំផុត: {recommendations[0].match_percentage:.1f}%")
    
    def run_demo(self):
        """Run the complete CLI demo."""
        # Initialize
        if not self.initialize():
            return False
        
        # Welcome
        self.show_welcome()
        
        # Wait for user to start
        input("\n⏎ ចុច Enter ដើម្បីចាប់ផ្តើម...")
        
        # Run assessment
        if not self.run_assessment():
            return False
        
        # Generate recommendations
        self.generate_recommendations()
        
        # End message
        print("\n🎉 ការវាយតម្លៃបានបញ្ចប់!")
        print("=" * 60)
        print("📞 ទំនាក់ទំនង: EduGuideBot Team")
        print("🌐 គេហទំព័រ: https://eduguidebot.kh")
        print("📧 អ៊ីមែល: <EMAIL>")
        print("\n👋 អរគុណសម្រាប់ការប្រើប្រាស់ EduGuideBot!")
        
        return True

def main():
    """Main function with graceful interruption handling."""
    import argparse

    parser = argparse.ArgumentParser(description='EduGuideBot CLI Demo')
    parser.add_argument('--quick', action='store_true', help='Quick performance test')
    args = parser.parse_args()

    if args.quick:
        # Quick performance test
        print("⚡ QUICK PERFORMANCE TEST")
        print("=" * 30)

        import time
        start = time.perf_counter()

        demo = CLIDemo()
        if demo.initialize():
            end = time.perf_counter()
            init_ms = (end - start) * 1000
            print(f"✅ Initialization: {init_ms:.1f} ms")

            # Quick recommendation test
            test_answers = {
                'location': 'phnom_penh',
                'budget': 'mid',
                'interest_field': 'stem'
            }

            start = time.perf_counter()
            recommendations = demo.engine.get_recommendations(test_answers, limit=3)
            end = time.perf_counter()

            comp_ms = (end - start) * 1000
            print(f"✅ Computation: {comp_ms:.1f} ms")

            if comp_ms < 50:
                print("🚀 Performance: EXCELLENT")
            elif comp_ms < 100:
                print("✅ Performance: GOOD")
            else:
                print("⚠️ Performance: NEEDS OPTIMIZATION")

            return
        else:
            print("❌ Quick test failed")
            return

    # Full demo
    print("🎓 EDUGUIDEBOT 2025 - CLI DEMO")
    print("=" * 60)
    print("📱 ការបង្ហាញដោយមិនត្រូវការអ៊ីនធឺណិត")
    print("🔧 ប្រព័ន្ធ MCDA ដូចគ្នានឹង Telegram Bot")
    print("=" * 60)

    demo = CLIDemo()

    try:
        success = demo.run_demo()
        if success:
            print("\n✅ ការបង្ហាញបានបញ្ចប់ដោយជោគជ័យ!")
        else:
            print("\n⚠️ ការបង្ហាញត្រូវបានបញ្ចប់មុនពេល")
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted gracefully.")
        print("✅ ការបង្ហាញត្រូវបានបញ្ចប់ដោយអ្នកប្រើប្រាស់")
    except EOFError:
        print("\n\n👋 Demo ended gracefully.")
        print("✅ ការបង្ហាញបានបញ្ចប់")
    except Exception as e:
        print(f"\n❌ កំហុសក្នុងការបង្ហាញ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
