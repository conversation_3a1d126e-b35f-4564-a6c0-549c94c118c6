# Demo Images & GIFs

## 📸 Recording Instructions

### For Telegram Bot Demo:
1. Use phone screen recording or desktop capture
2. Show complete flow: /start → assessment → recommendations → details
3. Keep recording under 30 seconds for GitHub display
4. Save as `telegram-demo.gif`

### For CLI Demo:
1. Use `asciinema` for terminal recording:
   ```bash
   # Install asciinema
   pip install asciinema
   
   # Record CLI demo
   asciinema rec cli-demo.cast
   python demo_cli.py
   # Complete a short assessment
   exit
   
   # Convert to GIF
   agg cli-demo.cast cli-demo.gif
   ```

### Optimization:
- Keep GIFs under 10MB for GitHub
- Use tools like `gifsicle` to compress:
  ```bash
  gifsicle -O3 --lossy=80 input.gif -o output.gif
  ```

## 🎯 Demo Script

### Telegram Demo (30 seconds):
1. Show /start command (3s)
2. Answer 3-4 key questions quickly (15s)
3. Show recommendation results (8s)
4. Click details for one recommendation (4s)

### CLI Demo (45 seconds):
1. Run `python demo_cli.py` (3s)
2. Show welcome screen (5s)
3. Answer 3-4 questions (20s)
4. Show timing and recommendations (12s)
5. Show final summary (5s)

## 📝 Placeholder Files

Until recordings are made, placeholder images are used:
- `telegram-demo.gif` - Telegram bot demonstration
- `cli-demo.gif` - CLI demo with timing

These should be replaced with actual recordings before final presentation.
