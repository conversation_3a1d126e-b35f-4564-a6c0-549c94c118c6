# EduGuideBot 2025 - Simple Requirements
# Essential dependencies for deployment

# Telegram Bot API (REQUIRED for bot functionality)
python-telegram-bot>=20.0

# Optional: Only install if you want to try enhanced features
# Note: These may not work in current execution environment
# pandas>=1.5.0
# numpy>=1.20.0
# scikit-learn>=1.0.0

# The bot works with basic Python libraries only:
# - json (built-in)
# - os (built-in)
# - math (built-in)
# - logging (built-in)
# - datetime (built-in)
# - collections (built-in)
