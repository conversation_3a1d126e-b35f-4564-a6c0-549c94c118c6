#!/usr/bin/env bash
# EduGuideBot 2025 - Speed-Without-Slop Bootstrap
# 15-minute setup for development environment

set -e

echo "🚀 EduGuideBot 2025 - Development Bootstrap"
echo "============================================"

# Check Python version
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python 3.8+ required, found $python_version"
    exit 1
fi

echo "✅ Python $python_version detected"

# Install development tools
echo "🔧 Installing development tools..."
pip3 install --upgrade pip

# Core development dependencies
pip3 install watchfiles pytest-xdist rich black isort flake8

echo "✅ Development tools installed"

# Create directory structure
echo "📁 Setting up project structure..."
mkdir -p tools build src tests data

# Make tools executable
chmod +x tools/*.py

echo "✅ Project structure ready"

# Build initial assets
echo "🏗️ Building initial assets..."
if [ -f "data/weights_matrix.yaml" ]; then
    python3 tools/build_assets.py
    echo "✅ Assets built successfully"
else
    echo "⚠️ No weights_matrix.yaml found, skipping asset build"
fi

# Generate additional test personas
echo "🤖 Generating test personas..."
python3 tools/fake_personas.py --edge=5 --random=20 --output=data/generated_personas.yaml
echo "✅ Test personas generated"

# Run quick verification
echo "🧪 Running verification tests..."
if python3 test_week1.py; then
    echo "✅ All systems operational"
else
    echo "⚠️ Some tests failed, but environment is set up"
fi

# Create .gitignore if it doesn't exist
if [ ! -f ".gitignore" ]; then
    cat > .gitignore << EOF
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Project specific
*.log
*.hash
*.pkl
.pytest_cache/
EOF
    echo "✅ .gitignore created"
fi

echo ""
echo "🎉 Bootstrap complete!"
echo "============================================"
echo ""
echo "📋 Available commands:"
echo "  make dev        - Start bot with hot-reload"
echo "  make test-fast  - Run tests in parallel"
echo "  make format     - Format code"
echo "  make assets     - Rebuild assets"
echo "  make help       - Show all commands"
echo ""
echo "🚀 Quick start:"
echo "  1. Run 'make dev' to start development"
echo "  2. Edit files and see instant reload"
echo "  3. Run 'make test-fast' for quick testing"
echo ""
echo "✅ Ready for Week 2 development!"
