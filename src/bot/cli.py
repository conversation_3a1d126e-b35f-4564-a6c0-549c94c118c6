#!/usr/bin/env python3
"""
CLI Demo Application - Clean Implementation
================================================================

HONEST ABOUT WHAT IT DOES:
- Offline demo of MCDA engine
- Same 6 questions as Telegram bot
- Perfect Khmer language
- Real recommendations

FOR PRESENTATION BACKUP WHEN INTERNET FAILS
"""

import logging
from typing import Dict
try:
    from src.core.mcda import MCDAEngine
except ImportError:
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from core.mcda import MCDAEngine

logger = logging.getLogger(__name__)

# Same questions as Telegram bot
CORE_QUESTIONS = [
    {
        "key": "location",
        "question_kh": "អ្នកចង់សិក្សានៅទីក្រុងណា?",
        "options": [
            {"text": "ភ្នំពេញ", "value": "pp"},
            {"text": "សៀមរាប", "value": "sr"},
            {"text": "បាត់ដំបង", "value": "btb"},
            {"text": "អនឡាញ", "value": "online"}
        ]
    },
    {
        "key": "budget",
        "question_kh": "ថវិកាសិក្សារបស់អ្នកប្រមាណប៉ុន្មាន?",
        "options": [
            {"text": "ទាប (< $800/ឆ្នាំ)", "value": "low"},
            {"text": "មធ្យម ($800-1500/ឆ្នាំ)", "value": "mid"},
            {"text": "ខ្ពស់ (> $1500/ឆ្នាំ)", "value": "high"}
        ]
    },
    {
        "key": "field",
        "question_kh": "អ្នកចូលចិត្តសិក្សាផ្នែកណា?",
        "options": [
            {"text": "វិទ្យាសាស្ត្រ និងបច្ចេកវិទ្យា", "value": "stem"},
            {"text": "អាជីវកម្ម និងគ្រប់គ្រង", "value": "business"},
            {"text": "សិល្បៈ និងការរចនា", "value": "arts"},
            {"text": "សុខភាព និងវេជ្ជសាស្ត្រ", "value": "health"}
        ]
    },
    {
        "key": "career",
        "question_kh": "អ្នកចង់ធ្វើការក្នុងវិស័យណា?",
        "options": [
            {"text": "បច្ចេកវិទ្យាព័ត៌មាន", "value": "tech"},
            {"text": "គ្រប់គ្រងអាជីវកម្ម", "value": "management"},
            {"text": "ការរចនា និងច្នៃប្រឌិត", "value": "creative"},
            {"text": "មិនប្រាកដ", "value": "unsure"}
        ]
    },
    {
        "key": "study_mode",
        "question_kh": "អ្នកចូលចិត្តរបៀបសិក្សាណា?",
        "options": [
            {"text": "នៅសាកលវិទ្យាល័យ", "value": "on_campus"},
            {"text": "ចម្រុះ (ផ្ទាល់ + អនឡាញ)", "value": "blended"},
            {"text": "អនឡាញទាំងស្រុង", "value": "online"}
        ]
    },
    {
        "key": "priority",
        "question_kh": "អ្វីសំខាន់បំផុតសម្រាប់អ្នក?",
        "options": [
            {"text": "ការងារងាយស្វែងរក", "value": "employment"},
            {"text": "ប្រាក់ខែខ្ពស់", "value": "salary"},
            {"text": "ចំណាប់អារម្មណ៍ផ្ទាល់ខ្លួន", "value": "interest"},
            {"text": "ស្ថានភាពសង្គម", "value": "prestige"}
        ]
    }
]

class CLIDemo:
    """CLI demo application."""
    
    def __init__(self):
        self.mcda_engine = MCDAEngine()
        self.answers: Dict[str, str] = {}
    
    def run(self):
        """Run the CLI demo."""
        print("🎓 EDUGUIDEBOT 2025 - CLI DEMO")
        print("=" * 60)
        print("📱 ការបង្ហាញដោយមិនត្រូវការអ៊ីនធឺណិត")
        print("🔧 ប្រព័ន្ធ MCDA ដូចគ្នានឹង Telegram Bot")
        print("=" * 60)
        
        if not self.mcda_engine.is_ready:
            print("❌ MCDA engine not ready")
            return False
        
        try:
            # Ask all questions
            for i, question in enumerate(CORE_QUESTIONS):
                self.ask_question(i + 1, question)
            
            # Generate recommendations
            print("\n🔄 កំពុងបង្កើតការណែនាំ...")
            recommendations = self.mcda_engine.get_recommendations(self.answers, limit=3)
            
            # Display results
            self.display_recommendations(recommendations)
            
            return True
            
        except KeyboardInterrupt:
            print("\n\n👋 ការបង្ហាញត្រូវបានបញ្ចប់ដោយអ្នកប្រើប្រាស់")
            return False
        except Exception as e:
            print(f"\n❌ កំហុសក្នុងការបង្ហាញ: {e}")
            return False
    
    def ask_question(self, question_num: int, question: Dict):
        """Ask a single question."""
        print(f"\n📊 សំណួរ {question_num}/{len(CORE_QUESTIONS)}")
        print(f"❓ {question['question_kh']}")
        print()
        
        # Display options
        for i, option in enumerate(question['options'], 1):
            print(f"  {i}. {option['text']}")
        
        # Get user input
        while True:
            try:
                choice = input(f"\nជ្រើសរើស (1-{len(question['options'])}): ").strip()
                choice_num = int(choice)
                
                if 1 <= choice_num <= len(question['options']):
                    selected_option = question['options'][choice_num - 1]
                    self.answers[question['key']] = selected_option['value']
                    print(f"✅ បានជ្រើសរើស: {selected_option['text']}")
                    break
                else:
                    print("❌ សូមជ្រើសរើសលេខពី 1 ដល់ " + str(len(question['options'])))
            except ValueError:
                print("❌ សូមបញ្ចូលលេខ")
            except KeyboardInterrupt:
                raise
    
    def display_recommendations(self, recommendations):
        """Display recommendations."""
        if not recommendations:
            print("😔 សូមទោស! មិនអាចស្វែងរកការណែនាំបានទេ។")
            return
        
        print("\n🎯 ការណែនាំសាកលវិទ្យាល័យសម្រាប់អ្នក")
        print("=" * 60)
        
        for i, rec in enumerate(recommendations, 1):
            print(f"\n{i}. {rec.program.university_name_kh}")
            print(f"   📚 {rec.program.major_name_kh}")
            print(f"   📍 {rec.program.city}")
            print(f"   💰 ${rec.program.tuition_usd:.0f}/ឆ្នាំ")
            print(f"   🎯 ពិន្ទុផ្គូផ្គង: {rec.match_percentage:.1f}%")
            print(f"   📈 អត្រាការងារ: {rec.program.employment_rate*100:.0f}%")
            print(f"   💵 ប្រាក់ខែចាប់ផ្តើម: ${rec.program.starting_salary_usd:.0f}")
            
            # Show explanation
            print(f"\n   💡 ហេតុផលណែនាំ:")
            explanation_lines = rec.explanation_kh.split('\n')
            for line in explanation_lines:
                if line.strip():
                    print(f"      {line}")
        
        print("\n✅ ការវាយតម្លៃបានបញ្ចប់!")
        print("🔄 ចុច Ctrl+C ដើម្បីចេញ ឬ Enter ដើម្បីបន្ត")
        
        try:
            input()
        except KeyboardInterrupt:
            pass

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='EduGuideBot CLI Demo')
    parser.add_argument('--quick', action='store_true', help='Quick performance test')
    args = parser.parse_args()
    
    if args.quick:
        # Quick performance test
        print("⚡ QUICK PERFORMANCE TEST")
        print("=" * 30)
        
        import time
        start = time.perf_counter()
        
        demo = CLIDemo()
        if demo.mcda_engine.is_ready:
            end = time.perf_counter()
            init_ms = (end - start) * 1000
            print(f"✅ Initialization: {init_ms:.1f} ms")
            
            # Quick recommendation test
            test_answers = {
                'location': 'pp',
                'budget': 'mid',
                'field': 'stem',
                'career': 'tech',
                'study_mode': 'on_campus',
                'priority': 'employment'
            }
            
            start = time.perf_counter()
            recommendations = demo.mcda_engine.get_recommendations(test_answers, limit=3)
            end = time.perf_counter()
            
            comp_ms = (end - start) * 1000
            print(f"✅ Computation: {comp_ms:.1f} ms")
            
            if comp_ms < 50:
                print("🚀 Performance: EXCELLENT")
            elif comp_ms < 100:
                print("✅ Performance: GOOD")
            else:
                print("⚠️ Performance: NEEDS OPTIMIZATION")
            
            return
        else:
            print("❌ Quick test failed")
            return
    
    # Full demo
    demo = CLIDemo()
    demo.run()

if __name__ == "__main__":
    main()
