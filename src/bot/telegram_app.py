#!/usr/bin/env python3
"""
Telegram Bot Application - Clean Implementation
================================================================

HONEST ABOUT WHAT IT DOES:
- 6 core questions in perfect Khmer
- Real MCDA recommendations
- Clean, testable code structure
- No hardcoded tokens (uses environment variables)

100% KHMER LANGUAGE IMPLEMENTATION
"""

import os
import logging
from typing import Dict, Any
from datetime import datetime

# Telegram imports with fallback
try:
    from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
    from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False

from src.core.mcda import MCDAEngine

logger = logging.getLogger(__name__)

# 6 Core Assessment Questions in Khmer
CORE_QUESTIONS = [
    {
        "key": "location",
        "question_kh": "អ្នកចង់សិក្សានៅទីក្រុងណា?",
        "options": [
            {"text": "ភ្នំពេញ", "value": "pp"},
            {"text": "សៀមរាប", "value": "sr"},
            {"text": "បាត់ដំបង", "value": "btb"},
            {"text": "អនឡាញ", "value": "online"}
        ]
    },
    {
        "key": "budget",
        "question_kh": "ថវិកាសិក្សារបស់អ្នកប្រមាណប៉ុន្មាន?",
        "options": [
            {"text": "ទាប (< $800/ឆ្នាំ)", "value": "low"},
            {"text": "មធ្យម ($800-1500/ឆ្នាំ)", "value": "mid"},
            {"text": "ខ្ពស់ (> $1500/ឆ្នាំ)", "value": "high"}
        ]
    },
    {
        "key": "field",
        "question_kh": "អ្នកចូលចិត្តសិក្សាផ្នែកណា?",
        "options": [
            {"text": "វិទ្យាសាស្ត្រ និងបច្ចេកវិទ្យា", "value": "stem"},
            {"text": "អាជីវកម្ម និងគ្រប់គ្រង", "value": "business"},
            {"text": "សិល្បៈ និងការរចនា", "value": "arts"},
            {"text": "សុខភាព និងវេជ្ជសាស្ត្រ", "value": "health"}
        ]
    },
    {
        "key": "career",
        "question_kh": "អ្នកចង់ធ្វើការក្នុងវិស័យណា?",
        "options": [
            {"text": "បច្ចេកវិទ្យាព័ត៌មាន", "value": "tech"},
            {"text": "គ្រប់គ្រងអាជីវកម្ម", "value": "management"},
            {"text": "ការរចនា និងច្នៃប្រឌិត", "value": "creative"},
            {"text": "មិនប្រាកដ", "value": "unsure"}
        ]
    },
    {
        "key": "study_mode",
        "question_kh": "អ្នកចូលចិត្តរបៀបសិក្សាណា?",
        "options": [
            {"text": "នៅសាកលវិទ្យាល័យ", "value": "on_campus"},
            {"text": "ចម្រុះ (ផ្ទាល់ + អនឡាញ)", "value": "blended"},
            {"text": "អនឡាញទាំងស្រុង", "value": "online"}
        ]
    },
    {
        "key": "priority",
        "question_kh": "អ្វីសំខាន់បំផុតសម្រាប់អ្នក?",
        "options": [
            {"text": "ការងារងាយស្វែងរក", "value": "employment"},
            {"text": "ប្រាក់ខែខ្ពស់", "value": "salary"},
            {"text": "ចំណាប់អារម្មណ៍ផ្ទាល់ខ្លួន", "value": "interest"},
            {"text": "ស្ថានភាពសង្គម", "value": "prestige"}
        ]
    }
]

# User sessions storage
user_sessions: Dict[int, Dict[str, Any]] = {}

class EduGuideBotApp:
    """Clean Telegram bot application."""
    
    def __init__(self):
        self.mcda_engine = MCDAEngine()
        self.application = None
        self.is_ready = False
        
        if not TELEGRAM_AVAILABLE:
            logger.warning("⚠️ Telegram libraries not available")
            return
        
        # Get bot token from environment
        self.bot_token = os.getenv('BOT_TOKEN')
        if not self.bot_token:
            logger.error("❌ BOT_TOKEN environment variable not set")
            return
        
        self._setup_application()
    
    def _setup_application(self):
        """Setup Telegram application with handlers."""
        try:
            self.application = Application.builder().token(self.bot_token).build()
            
            # Add handlers
            self.application.add_handler(CommandHandler("start", self.start_command))
            self.application.add_handler(CommandHandler("help", self.help_command))
            self.application.add_handler(CallbackQueryHandler(self.handle_callback))
            
            self.is_ready = True
            logger.info("✅ Bot application setup complete")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup bot application: {e}")
            self.is_ready = False
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command."""
        user_id = update.effective_user.id
        
        # Initialize user session
        user_sessions[user_id] = {
            'current_question': 0,
            'answers': {},
            'started_at': datetime.now()
        }
        
        welcome_text = """🎓 **សូមស្វាគមន៍មកកាន់ EduGuideBot 2025!**

ខ្ញុំនឹងជួយអ្នកស្វែងរកសាកលវិទ្យាល័យ និងជំនាញដែលសមស្របបំផុត។

📊 **ប្រព័ន្ធ MCDA:**
• ការវិភាគពហុវិធានការ
• ការណែនាំដោយផ្អែកលើទិន្នន័យពិត
• ការពន្យល់ច្បាស់លាស់ជាភាសាខ្មែរ

🔍 **ដំណើរការ:**
• សំណួរ ៦ ចំណុចសំខាន់
• ការវិភាគលម្អិត
• ការណែនាំដែលមានហេតុផល

តោះចាប់ផ្តើម! 👇"""
        
        keyboard = [[InlineKeyboardButton("🚀 ចាប់ផ្តើមការវាយតម្លៃ", callback_data="start_assessment")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(welcome_text, reply_markup=reply_markup, parse_mode='Markdown')
        logger.info(f"👤 User {user_id} started assessment")
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command."""
        help_text = """🆘 **ជំនួយ EduGuideBot 2025**

**ពាក្យបញ្ជាដែលមាន:**
• `/start` - ចាប់ផ្តើមការវាយតម្លៃ
• `/help` - បង្ហាញជំនួយនេះ

**របៀបប្រើប្រាស់:**
1. ចុច /start ដើម្បីចាប់ផ្តើម
2. ឆ្លើយសំណួរ ៦ ចំណុច
3. ទទួលបានការណែនាំ
4. មើលព័ត៌មានលម្អិត

**ជំនួយបន្ថែម:**
ប្រសិនបើមានបញ្ហា សូមចាប់ផ្តើមម្តងទៀតដោយចុច /start"""
        
        await update.message.reply_text(help_text, parse_mode='Markdown')
    
    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle callback queries from inline keyboards."""
        query = update.callback_query
        await query.answer()
        
        user_id = query.from_user.id
        data = query.data
        
        try:
            if data == "start_assessment":
                await self.show_question(query, user_id, 0)
            elif data.startswith("answer_"):
                await self.process_answer(query, user_id, data)
            elif data.startswith("rec_"):
                await self.show_recommendation_details(query, user_id, data)
            elif data == "back_to_list":
                await self.show_recommendations_list(query, user_id)
        except Exception as e:
            logger.error(f"❌ Error handling callback {data}: {e}")
            await query.edit_message_text("😔 មានបញ្ហាបច្ចេកទេស។ សូមព្យាយាមម្តងទៀត។")
    
    async def show_question(self, query, user_id: int, question_index: int):
        """Show assessment question to user."""
        if question_index >= len(CORE_QUESTIONS):
            await self.generate_recommendations(query, user_id)
            return
        
        question = CORE_QUESTIONS[question_index]
        
        progress = f"📊 សំណួរ {question_index + 1}/{len(CORE_QUESTIONS)}"
        question_text = f"{progress}\n\n❓ **{question['question_kh']}**"
        
        # Create option buttons
        keyboard = []
        for option in question['options']:
            callback_data = f"answer_{question_index}_{option['value']}"
            keyboard.append([InlineKeyboardButton(option['text'], callback_data=callback_data)])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(question_text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def process_answer(self, query, user_id: int, callback_data: str):
        """Process user's answer and move to next question."""
        # Parse callback data: answer_{question_index}_{value}
        parts = callback_data.split('_', 2)
        question_index = int(parts[1])
        answer_value = parts[2]
        
        # Store answer
        if user_id not in user_sessions:
            user_sessions[user_id] = {'answers': {}, 'current_question': 0}
        
        question = CORE_QUESTIONS[question_index]
        user_sessions[user_id]['answers'][question['key']] = answer_value
        user_sessions[user_id]['current_question'] = question_index + 1
        
        logger.info(f"👤 User {user_id} answered Q{question_index + 1}: {answer_value}")
        
        # Show next question
        await self.show_question(query, user_id, question_index + 1)
    
    async def generate_recommendations(self, query, user_id: int):
        """Generate and display MCDA recommendations."""
        if user_id not in user_sessions:
            await query.edit_message_text("❌ សម័យសិក្សាបានផុតកំណត់។ សូមចាប់ផ្តើមម្តងទៀត។")
            return
        
        user_answers = user_sessions[user_id]['answers']
        
        # Show processing message
        await query.edit_message_text("🔄 **កំពុងវិភាគ និងបង្កើតការណែនាំ...**\n\n⏳ សូមរង់ចាំបន្តិច", parse_mode='Markdown')
        
        try:
            # Generate MCDA recommendations
            recommendations = self.mcda_engine.get_recommendations(user_answers, limit=5)
            
            if not recommendations:
                await query.edit_message_text("😔 សូមទោស! មិនអាចស្វែងរកការណែនាំបានទេ។ សូមព្យាយាមម្តងទៀត។")
                return
            
            # Store recommendations in session
            user_sessions[user_id]['recommendations'] = recommendations
            
            await self.show_recommendations_list(query, user_id)
            
            logger.info(f"✅ Generated {len(recommendations)} recommendations for user {user_id}")
            
        except Exception as e:
            logger.error(f"❌ Error generating recommendations for user {user_id}: {e}")
            await query.edit_message_text("😔 មានបញ្ហាក្នុងការបង្កើតការណែនាំ។ សូមព្យាយាមម្តងទៀត។")
    
    async def show_recommendations_list(self, query, user_id: int):
        """Show list of recommendations."""
        if user_id not in user_sessions or 'recommendations' not in user_sessions[user_id]:
            await query.edit_message_text("❌ ព័ត៌មានការណែនាំមិនមានទេ។")
            return
        
        recommendations = user_sessions[user_id]['recommendations']
        
        # Format recommendations display
        result_text = "🎯 **ការណែនាំសាកលវិទ្យាល័យសម្រាប់អ្នក**\n\n"
        result_text += f"📊 រកឃើញ {len(recommendations)} ជម្រើសល្អបំផុត:\n\n"
        
        keyboard = []
        
        for i, rec in enumerate(recommendations, 1):
            # Format recommendation card
            match_percent = f"{rec.match_percentage:.1f}%"
            result_text += f"**{i}. {rec.program.university_name_kh}**\n"
            result_text += f"📚 {rec.program.major_name_kh}\n"
            result_text += f"📍 {rec.program.city} • 🎯 {match_percent}\n\n"
            
            # Add detail button
            callback_data = f"rec_{i-1}"
            keyboard.append([InlineKeyboardButton(f"📋 ព័ត៌មានលម្អិត {i}", callback_data=callback_data)])
        
        # Add restart button
        keyboard.append([InlineKeyboardButton("🔄 ធ្វើការវាយតម្លៃថ្មី", callback_data="start_assessment")])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(result_text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def show_recommendation_details(self, query, user_id: int, callback_data: str):
        """Show detailed information for a specific recommendation."""
        if user_id not in user_sessions or 'recommendations' not in user_sessions[user_id]:
            await query.edit_message_text("❌ ព័ត៌មានការណែនាំមិនមានទេ។")
            return
        
        # Parse recommendation index
        rec_index = int(callback_data.split('_')[1])
        recommendations = user_sessions[user_id]['recommendations']
        
        if rec_index >= len(recommendations):
            await query.edit_message_text("❌ ការណែនាំមិនត្រឹមត្រូវ។")
            return
        
        rec = recommendations[rec_index]
        program = rec.program
        
        # Format detailed information
        detail_text = f"🎓 **{program.university_name_kh}**\n"
        detail_text += f"📚 **{program.major_name_kh}**\n\n"
        
        detail_text += f"📊 **ពិន្ទុផ្គូផ្គង:** {rec.match_percentage:.1f}%\n"
        detail_text += f"📍 **ទីតាំង:** {program.city}\n"
        detail_text += f"💰 **ថ្លៃសិក្សា:** ${program.tuition_usd:.0f}/ឆ្នាំ\n"
        detail_text += f"📈 **អត្រាការងារ:** {program.employment_rate*100:.0f}%\n"
        detail_text += f"💵 **ប្រាក់ខែចាប់ផ្តើម:** ${program.starting_salary_usd:.0f}\n\n"
        
        detail_text += f"💡 **ហេតុផលណែនាំ:**\n{rec.explanation_kh}\n\n"
        
        # Navigation buttons
        keyboard = []
        
        # Previous/Next recommendation buttons
        nav_buttons = []
        if rec_index > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ មុន", callback_data=f"rec_{rec_index-1}"))
        if rec_index < len(recommendations) - 1:
            nav_buttons.append(InlineKeyboardButton("➡️ បន្ទាប់", callback_data=f"rec_{rec_index+1}"))
        
        if nav_buttons:
            keyboard.append(nav_buttons)
        
        # Back to list and restart buttons
        keyboard.append([InlineKeyboardButton("📋 ត្រលប់ទៅបញ្ជី", callback_data="back_to_list")])
        keyboard.append([InlineKeyboardButton("🔄 ធ្វើការវាយតម្លៃថ្មី", callback_data="start_assessment")])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(detail_text, reply_markup=reply_markup, parse_mode='Markdown')
    
    def run(self):
        """Run the bot application."""
        if not TELEGRAM_AVAILABLE:
            logger.error("❌ Telegram libraries not available")
            logger.info("📝 Install with: pip install python-telegram-bot")
            return
        
        if not self.is_ready:
            logger.error("❌ Bot not ready - check BOT_TOKEN environment variable")
            return
        
        logger.info("🚀 Starting EduGuideBot 2025...")
        logger.info("📱 Bot ready for Telegram interactions")
        
        # Run the bot
        self.application.run_polling(drop_pending_updates=True)

def main():
    """Main entry point."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("🎓 EDUGUIDEBOT 2025 - CLEAN SLATE")
    logger.info("=" * 50)
    logger.info("✅ 6 Core Questions in Khmer")
    logger.info("✅ Clean MCDA Implementation")
    logger.info("✅ Real University Data")
    logger.info("✅ Transparent Scoring")
    logger.info("=" * 50)
    
    app = EduGuideBotApp()
    app.run()

if __name__ == "__main__":
    main()
