#!/usr/bin/env python3
"""
ML Ranker - Machine Learning Recommendation Engine
================================================================

Gradient-boosted trees on top of MCDA outputs + raw features.
Swappable interface: rank(programs, answers) -> ranked_list
"""

import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import LabelEncoder
import joblib

logger = logging.getLogger(__name__)

class MLRanker:
    """ML-based program ranker using RandomForest."""
    
    def __init__(self, model_path: str = "build/ml_model.joblib"):
        self.model_path = Path(model_path)
        self.model: Optional[RandomForestRegressor] = None
        self.label_encoders: Dict[str, LabelEncoder] = {}
        self.feature_columns: List[str] = []
        self.is_ready = False
        
        self._load_model()
    
    def _load_model(self):
        """Load trained model if available."""
        try:
            if self.model_path.exists():
                model_data = joblib.load(self.model_path)
                self.model = model_data['model']
                self.label_encoders = model_data['label_encoders']
                self.feature_columns = model_data['feature_columns']
                self.is_ready = True
                logger.info(f"✅ ML model loaded from {self.model_path}")
            else:
                logger.warning(f"⚠️ No trained model found at {self.model_path}")
                self.is_ready = False
        except Exception as e:
            logger.error(f"❌ Failed to load ML model: {e}")
            self.is_ready = False
    
    def train(self, training_data_path: str = "build/training_data.csv"):
        """Train the ML model on synthetic data."""
        training_file = Path(training_data_path)
        
        if not training_file.exists():
            logger.error(f"❌ Training data not found: {training_file}")
            return False
        
        try:
            # Load training data
            df = pd.read_csv(training_file)
            logger.info(f"📊 Loaded training data: {len(df)} samples")
            
            # Prepare features
            X, y = self._prepare_features(df)
            
            # Train model with better parameters for score spread
            self.model = RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
            
            self.model.fit(X, y)
            
            # Save model
            self._save_model()
            
            self.is_ready = True
            logger.info("✅ ML model trained successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to train ML model: {e}")
            return False
    
    def _prepare_features(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare features for training/prediction."""
        # Define feature columns
        categorical_features = ['city', 'field_tag', 'career_cluster']
        numerical_features = ['tuition_usd', 'employment_rate', 'starting_salary_usd']
        
        # User preference features (from answers)
        user_features = ['location_match', 'field_match', 'budget_match', 'career_match']
        
        self.feature_columns = categorical_features + numerical_features + user_features
        
        # Encode categorical features
        X_encoded = df.copy()
        
        for col in categorical_features:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()
                X_encoded[col] = self.label_encoders[col].fit_transform(df[col].astype(str))
            else:
                X_encoded[col] = self.label_encoders[col].transform(df[col].astype(str))
        
        # Select features
        X = X_encoded[self.feature_columns].values
        y = df['match_score'].values
        
        return X, y
    
    def _save_model(self):
        """Save trained model and encoders."""
        try:
            self.model_path.parent.mkdir(parents=True, exist_ok=True)
            
            model_data = {
                'model': self.model,
                'label_encoders': self.label_encoders,
                'feature_columns': self.feature_columns
            }
            
            joblib.dump(model_data, self.model_path)
            logger.info(f"💾 Model saved to {self.model_path}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save model: {e}")
    
    def rank_programs(self, programs_df: pd.DataFrame, user_answers: Dict[str, str]) -> pd.DataFrame:
        """Rank programs using ML model."""
        if not self.is_ready:
            logger.warning("⚠️ ML model not ready, returning original order")
            return programs_df
        
        try:
            # Add user preference features
            enhanced_df = self._add_user_features(programs_df, user_answers)
            
            # Prepare features for prediction
            X = self._encode_features_for_prediction(enhanced_df)
            
            # Predict scores
            scores = self.model.predict(X)
            
            # Add scores and sort
            result_df = enhanced_df.copy()
            result_df['ml_score'] = scores
            result_df = result_df.sort_values('ml_score', ascending=False)
            
            logger.info(f"🤖 ML ranked {len(result_df)} programs")
            return result_df
            
        except Exception as e:
            logger.error(f"❌ ML ranking failed: {e}")
            return programs_df
    
    def _add_user_features(self, programs_df: pd.DataFrame, user_answers: Dict[str, str]) -> pd.DataFrame:
        """Add user preference matching features."""
        df = programs_df.copy()
        
        # Location match
        user_location = user_answers.get('location', '').upper()
        df['location_match'] = (df['city'] == user_location).astype(int)
        
        # Field match
        user_field = user_answers.get('field', '').upper()
        df['field_match'] = (df['field_tag'] == user_field).astype(int)
        
        # Budget match
        user_budget = user_answers.get('budget', 'mid')
        df['budget_match'] = df['tuition_usd'].apply(
            lambda x: self._calculate_budget_match(x, user_budget)
        )
        
        # Career match
        user_career = user_answers.get('career', '').upper()
        df['career_match'] = (df['career_cluster'] == user_career).astype(int)
        
        return df
    
    def _calculate_budget_match(self, tuition: float, budget_preference: str) -> int:
        """Calculate budget compatibility."""
        if budget_preference == 'low':
            return 1 if tuition <= 800 else 0
        elif budget_preference == 'mid':
            return 1 if 800 < tuition <= 1500 else 0
        elif budget_preference == 'high':
            return 1 if tuition > 1500 else 0
        return 0
    
    def _encode_features_for_prediction(self, df: pd.DataFrame) -> np.ndarray:
        """Encode features for prediction."""
        X_encoded = df.copy()
        
        # Encode categorical features
        categorical_features = ['city', 'field_tag', 'career_cluster']
        
        for col in categorical_features:
            if col in self.label_encoders:
                # Handle unseen categories
                known_categories = set(self.label_encoders[col].classes_)
                X_encoded[col] = df[col].astype(str).apply(
                    lambda x: x if x in known_categories else 'OTHER'
                )
                X_encoded[col] = self.label_encoders[col].transform(X_encoded[col])
            else:
                # Fallback encoding
                X_encoded[col] = 0
        
        # Select feature columns
        return X_encoded[self.feature_columns].values
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance from trained model."""
        if not self.is_ready or self.model is None:
            return {}
        
        try:
            importance = self.model.feature_importances_
            return dict(zip(self.feature_columns, importance))
        except Exception as e:
            logger.error(f"❌ Failed to get feature importance: {e}")
            return {}

def main():
    """Main function for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    logger.info("🤖 ML RANKER TEST")
    logger.info("=" * 30)
    
    ranker = MLRanker()
    
    if ranker.is_ready:
        logger.info("✅ ML ranker ready")
        
        # Show feature importance
        importance = ranker.get_feature_importance()
        if importance:
            logger.info("📊 Feature importance:")
            for feature, score in sorted(importance.items(), key=lambda x: x[1], reverse=True):
                logger.info(f"  {feature}: {score:.3f}")
    else:
        logger.info("⚠️ ML ranker not ready - need to train model first")

if __name__ == "__main__":
    main()
