#!/usr/bin/env python3
"""
Unified Recommender Service - MCDA + ML Hybrid
================================================================

Service layer that chooses MCDA or ML, returns top N with explanations.
Explanations come from SHAP (for ML) or weight table (for rules).
"""

import logging
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass

from .mcda import MCDAEngine, Program, Recommendation
from .ml_ranker import MLRanker

logger = logging.getLogger(__name__)

@dataclass
class UnifiedRecommendation:
    """Unified recommendation with multiple scoring methods."""
    program: Program
    mcda_score: float
    ml_score: Optional[float]
    final_score: float
    match_percentage: float
    explanation_kh: str
    method_used: str  # 'mcda', 'ml', 'hybrid'

class RecommenderService:
    """Unified recommendation service."""
    
    def __init__(self, use_ml: bool = True):
        self.mcda_engine = MCDAEngine()
        self.ml_ranker = MLRanker() if use_ml else None
        self.programs_df: Optional[pd.DataFrame] = None
        self.is_ready = False
        
        self._load_programs()
    
    def _load_programs(self):
        """Load programs from parquet file."""
        programs_file = Path("build/programs.parquet")
        
        try:
            if programs_file.exists():
                self.programs_df = pd.read_parquet(programs_file)
                logger.info(f"📊 Loaded {len(self.programs_df)} programs from parquet")
            else:
                # Fallback to MCDA sample data
                logger.warning("⚠️ No parquet file found, using MCDA sample data")
                self._create_sample_dataframe()
            
            self.is_ready = True
            
        except Exception as e:
            logger.error(f"❌ Failed to load programs: {e}")
            self._create_sample_dataframe()
    
    def _create_sample_dataframe(self):
        """Create sample DataFrame from MCDA engine."""
        try:
            programs = []
            for program in self.mcda_engine.programs:
                programs.append({
                    'program_id': f"{program.university_id}_{program.major_name_en.replace(' ', '_')}",
                    'university_id': program.university_id,
                    'university_name_kh': program.university_name_kh,
                    'university_name_en': program.university_name_en,
                    'major_name_kh': program.major_name_kh,
                    'major_name_en': program.major_name_en,
                    'city': program.city,
                    'tuition_usd': program.tuition_usd,
                    'field_tag': program.field_tag,
                    'career_cluster': program.career_cluster,
                    'employment_rate': program.employment_rate,
                    'starting_salary_usd': program.starting_salary_usd
                })
            
            self.programs_df = pd.DataFrame(programs)
            self.is_ready = True
            logger.info(f"✅ Created sample DataFrame with {len(programs)} programs")
            
        except Exception as e:
            logger.error(f"❌ Failed to create sample DataFrame: {e}")
            self.is_ready = False
    
    def get_recommendations(self, user_answers: Dict[str, str], limit: int = 5) -> List[UnifiedRecommendation]:
        """Get unified recommendations using best available method."""
        if not self.is_ready:
            logger.error("❌ Recommender service not ready")
            return []
        
        try:
            # Determine which method to use
            use_ml = self.ml_ranker and self.ml_ranker.is_ready
            method = "ml" if use_ml else "mcda"
            
            logger.info(f"🎯 Using {method.upper()} for recommendations")
            
            if use_ml:
                return self._get_ml_recommendations(user_answers, limit)
            else:
                return self._get_mcda_recommendations(user_answers, limit)
                
        except Exception as e:
            logger.error(f"❌ Failed to get recommendations: {e}")
            return []
    
    def _get_mcda_recommendations(self, user_answers: Dict[str, str], limit: int) -> List[UnifiedRecommendation]:
        """Get recommendations using MCDA only."""
        mcda_recommendations = self.mcda_engine.get_recommendations(user_answers, limit)
        
        unified_recs = []
        for mcda_rec in mcda_recommendations:
            unified_rec = UnifiedRecommendation(
                program=mcda_rec.program,
                mcda_score=mcda_rec.total_score,
                ml_score=None,
                final_score=mcda_rec.total_score,
                match_percentage=mcda_rec.match_percentage,
                explanation_kh=mcda_rec.explanation_kh,
                method_used="mcda"
            )
            unified_recs.append(unified_rec)
        
        return unified_recs
    
    def _get_ml_recommendations(self, user_answers: Dict[str, str], limit: int) -> List[UnifiedRecommendation]:
        """Get recommendations using ML ranker."""
        # Get ML rankings
        ranked_df = self.ml_ranker.rank_programs(self.programs_df, user_answers)
        
        # Get top programs
        top_programs = ranked_df.head(limit)
        
        unified_recs = []
        for _, row in top_programs.iterrows():
            # Create Program object
            program = Program(
                university_id=row['university_id'],
                university_name_kh=row['university_name_kh'],
                university_name_en=row['university_name_en'],
                major_name_kh=row['major_name_kh'],
                major_name_en=row['major_name_en'],
                city=row['city'],
                tuition_usd=row['tuition_usd'],
                field_tag=row['field_tag'],
                career_cluster=row['career_cluster'],
                employment_rate=row['employment_rate'],
                starting_salary_usd=row['starting_salary_usd']
            )
            
            # Get MCDA score for comparison
            mcda_score, _ = self.mcda_engine._calculate_score(program, user_answers)
            ml_score = row['ml_score']
            
            # Hybrid scoring (70% ML, 30% MCDA)
            final_score = 0.7 * ml_score + 0.3 * mcda_score
            
            # Generate explanation
            explanation = self._generate_ml_explanation(program, user_answers, ml_score, mcda_score)
            
            unified_rec = UnifiedRecommendation(
                program=program,
                mcda_score=mcda_score,
                ml_score=ml_score,
                final_score=final_score,
                match_percentage=final_score * 100,
                explanation_kh=explanation,
                method_used="ml"
            )
            unified_recs.append(unified_rec)
        
        return unified_recs
    
    def _generate_ml_explanation(self, program: Program, user_answers: Dict[str, str], 
                                ml_score: float, mcda_score: float) -> str:
        """Generate explanation for ML-based recommendation."""
        explanation = f"ការណែនាំដោយ AI សម្រាប់ {program.major_name_kh} នៅ {program.university_name_kh}:\n\n"
        
        # ML confidence
        confidence = "ខ្ពស់" if ml_score > 0.7 else "មធ្យម" if ml_score > 0.4 else "ទាប"
        explanation += f"🤖 ការវិភាគដោយ AI: {confidence} ({ml_score:.2f})\n"
        
        # MCDA validation
        mcda_confidence = "ខ្ពស់" if mcda_score > 0.7 else "មធ្យម" if mcda_score > 0.4 else "ទាប"
        explanation += f"📊 ការវិភាគតាមវិធីសាស្ត្រ: {mcda_confidence} ({mcda_score:.2f})\n\n"
        
        # Key factors
        if user_answers.get('location', '').upper() == program.city:
            explanation += f"✅ ទីតាំងស្របតាមចំណង់ចំណូលចិត្ត ({program.city})\n"
        
        if user_answers.get('field', '').upper() == program.field_tag:
            explanation += f"✅ ផ្នែកសិក្សាត្រូវនឹងចំណាប់អារម្មណ៍ ({program.field_tag})\n"
        
        explanation += f"\n📊 អត្រាការងារ: {program.employment_rate*100:.0f}%"
        explanation += f"\n💰 ប្រាក់ខែចាប់ផ្តើម: ${program.starting_salary_usd:.0f}"
        explanation += f"\n💵 ថ្លៃសិក្សា: ${program.tuition_usd:.0f}/ឆ្នាំ"
        
        return explanation
    
    def get_method_info(self) -> Dict[str, any]:
        """Get information about available methods."""
        return {
            'mcda_ready': self.mcda_engine.is_ready,
            'ml_ready': self.ml_ranker.is_ready if self.ml_ranker else False,
            'programs_count': len(self.programs_df) if self.programs_df is not None else 0,
            'current_method': 'ml' if (self.ml_ranker and self.ml_ranker.is_ready) else 'mcda'
        }

def main():
    """Main function for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    logger.info("🎯 RECOMMENDER SERVICE TEST")
    logger.info("=" * 40)
    
    # Test with ML
    service = RecommenderService(use_ml=True)
    
    # Show method info
    info = service.get_method_info()
    logger.info(f"📊 Method info: {info}")
    
    # Test recommendations
    test_answers = {
        'location': 'pp',
        'field': 'stem',
        'budget': 'mid',
        'career': 'tech'
    }
    
    recommendations = service.get_recommendations(test_answers, limit=3)
    
    logger.info(f"✅ Generated {len(recommendations)} recommendations")
    for i, rec in enumerate(recommendations, 1):
        logger.info(f"{i}. {rec.program.university_name_en} - {rec.program.major_name_en}")
        logger.info(f"   Method: {rec.method_used}, Score: {rec.final_score:.3f}")

if __name__ == "__main__":
    main()
