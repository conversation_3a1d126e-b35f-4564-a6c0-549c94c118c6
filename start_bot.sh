#!/bin/bash
# EduGuideBot 2025 - Optimized Bot Startup Script
# Handles memory optimization and graceful error handling

set -e

echo "🚀 EDUGUIDEBOT 2025 - OPTIMIZED STARTUP"
echo "======================================"

# Memory optimization
export PYTHONMALLOC=malloc
export PYTHONHASHSEED=0

# Check prerequisites
if [ ! -f ".env" ]; then
    echo "❌ .env file not found!"
    echo "💡 Creating from template..."
    cp .env.example .env
    echo "⚠️ Please edit .env with your BOT_TOKEN"
    exit 1
fi

# Check BOT_TOKEN
if ! grep -q "BOT_TOKEN=8198268528" .env; then
    echo "❌ BOT_TOKEN not properly set in .env"
    echo "💡 Please check your .env file"
    exit 1
fi

echo "✅ Environment validated"

# Build assets if needed
if [ ! -d "build" ] || [ ! -f "build/weights_matrix.pkl" ]; then
    echo "🔧 Building assets..."
    make assets
fi

echo "✅ Assets ready"

# Memory-optimized startup
echo "🤖 Starting bot with memory optimization..."
echo "📊 Memory limit: 150MB soft"
echo "⏳ Initializing..."

# Start with memory monitoring
python -X tracemalloc=1 src/bot/app.py

echo "👋 Bot stopped"
