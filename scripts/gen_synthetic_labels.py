#!/usr/bin/env python3
"""
Synthetic Label Generator
================================================================

Generates synthetic training data with rules-derived labels.
Creates realistic user profiles and their ideal program matches.
"""

import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import random

logger = logging.getLogger(__name__)

# User profile templates
USER_PROFILES = [
    {
        'location': 'pp', 'field': 'stem', 'budget': 'mid', 'career': 'tech',
        'description': 'Tech-focused student in Phnom Penh'
    },
    {
        'location': 'pp', 'field': 'business', 'budget': 'high', 'career': 'mgt',
        'description': 'Business student with high budget'
    },
    {
        'location': 'sr', 'field': 'arts', 'budget': 'low', 'career': 'creative',
        'description': 'Arts student in Siem Reap'
    },
    {
        'location': 'pp', 'field': 'health', 'budget': 'mid', 'career': 'health',
        'description': 'Health sciences student'
    },
    {
        'location': 'btb', 'field': 'stem', 'budget': 'low', 'career': 'engr',
        'description': 'Engineering student in Battambang'
    }
]

def load_programs() -> pd.DataFrame:
    """Load programs from parquet file."""
    programs_file = Path("build/programs.parquet")
    
    if not programs_file.exists():
        logger.error(f"❌ Programs file not found: {programs_file}")
        logger.info("💡 Run 'make assets' first to build programs.parquet")
        return pd.DataFrame()
    
    try:
        df = pd.read_parquet(programs_file)
        logger.info(f"📊 Loaded {len(df)} programs")
        return df
    except Exception as e:
        logger.error(f"❌ Failed to load programs: {e}")
        return pd.DataFrame()

def calculate_oracle_score(program: Dict, user_profile: Dict) -> float:
    """Calculate oracle score based on logical rules."""
    score = 0.0
    
    # Location match (25% weight)
    if program['city'].lower() == user_profile['location']:
        score += 0.25
    elif user_profile['location'] == 'pp':  # PP students more flexible
        score += 0.15
    else:
        score += 0.05
    
    # Field match (30% weight)
    program_field = program['field_tag'].lower()
    user_field = user_profile['field']
    
    if program_field == user_field:
        score += 0.30
    elif (program_field == 'stem' and user_field in ['tech', 'engr']) or \
         (program_field == 'bus' and user_field in ['business', 'mgt']):
        score += 0.20
    else:
        score += 0.05
    
    # Budget match (20% weight)
    tuition = program['tuition_usd']
    budget = user_profile['budget']
    
    if budget == 'low' and tuition <= 800:
        score += 0.20
    elif budget == 'mid' and 800 < tuition <= 1500:
        score += 0.20
    elif budget == 'high' and tuition > 1500:
        score += 0.20
    elif budget == 'mid' and tuition <= 800:  # Mid budget, low cost = good
        score += 0.15
    else:
        score += 0.05
    
    # Career match (15% weight)
    program_career = program['career_cluster'].lower()
    user_career = user_profile['career']
    
    if program_career == user_career:
        score += 0.15
    elif (program_career == 'tech' and user_career in ['tech', 'stem']) or \
         (program_career == 'mgt' and user_career in ['business', 'mgt']):
        score += 0.10
    else:
        score += 0.02
    
    # Employment prospects (10% weight)
    employment_rate = program['employment_rate']
    score += employment_rate * 0.10
    
    # Add more significant noise to create better score spread
    noise = random.uniform(-0.15, 0.15)
    score = max(0.0, min(1.0, score + noise))
    
    return score

def generate_user_variations(base_profiles: List[Dict], num_variations: int = 50) -> List[Dict]:
    """Generate variations of base user profiles."""
    variations = []
    
    for _ in range(num_variations):
        # Pick a random base profile
        base = random.choice(base_profiles)
        
        # Create variation
        variation = base.copy()
        
        # Randomly modify some attributes (20% chance each)
        if random.random() < 0.2:
            variation['location'] = random.choice(['pp', 'sr', 'btb'])
        
        if random.random() < 0.2:
            variation['field'] = random.choice(['stem', 'business', 'arts', 'health'])
        
        if random.random() < 0.2:
            variation['budget'] = random.choice(['low', 'mid', 'high'])
        
        if random.random() < 0.2:
            variation['career'] = random.choice(['tech', 'mgt', 'engr', 'creative', 'health'])
        
        variations.append(variation)
    
    return variations

def generate_training_data(programs_df: pd.DataFrame, num_samples: int = 1000) -> pd.DataFrame:
    """Generate synthetic training data."""
    logger.info(f"🎲 Generating {num_samples} synthetic training samples...")
    
    # Generate user profiles
    all_profiles = USER_PROFILES + generate_user_variations(USER_PROFILES, num_samples // 10)
    
    training_data = []
    
    for i in range(num_samples):
        # Pick random user profile and program
        user_profile = random.choice(all_profiles)
        program_idx = random.randint(0, len(programs_df) - 1)
        program = programs_df.iloc[program_idx].to_dict()
        
        # Calculate oracle score
        match_score = calculate_oracle_score(program, user_profile)
        
        # Add user preference features
        sample = program.copy()
        sample.update({
            'user_location': user_profile['location'],
            'user_field': user_profile['field'],
            'user_budget': user_profile['budget'],
            'user_career': user_profile['career'],
            'location_match': int(program['city'].lower() == user_profile['location']),
            'field_match': int(program['field_tag'].lower() == user_profile['field']),
            'budget_match': calculate_budget_match(program['tuition_usd'], user_profile['budget']),
            'career_match': int(program['career_cluster'].lower() == user_profile['career']),
            'match_score': match_score
        })
        
        training_data.append(sample)
    
    df = pd.DataFrame(training_data)
    logger.info(f"✅ Generated training data with {len(df)} samples")
    logger.info(f"📊 Score distribution: min={df['match_score'].min():.3f}, max={df['match_score'].max():.3f}, mean={df['match_score'].mean():.3f}")
    
    return df

def calculate_budget_match(tuition: float, budget_preference: str) -> int:
    """Calculate budget compatibility."""
    if budget_preference == 'low':
        return 1 if tuition <= 800 else 0
    elif budget_preference == 'mid':
        return 1 if 800 < tuition <= 1500 else 0
    elif budget_preference == 'high':
        return 1 if tuition > 1500 else 0
    return 0

def save_training_data(df: pd.DataFrame, output_path: str = "build/training_data.csv"):
    """Save training data to CSV."""
    output_file = Path(output_path)
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    df.to_csv(output_file, index=False)
    logger.info(f"💾 Saved training data to {output_file}")

def main():
    """Main function."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    logger.info("🎲 SYNTHETIC LABEL GENERATOR")
    logger.info("=" * 40)
    
    # Load programs
    programs_df = load_programs()
    
    if programs_df.empty:
        logger.error("❌ No programs available for training data generation")
        return
    
    # Generate training data
    training_df = generate_training_data(programs_df, num_samples=1000)
    
    # Save training data
    save_training_data(training_df)
    
    logger.info("🎉 Synthetic training data generated successfully!")

if __name__ == "__main__":
    main()
