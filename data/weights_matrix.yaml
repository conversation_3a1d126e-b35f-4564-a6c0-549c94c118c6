# MCDA Weight Matrix for EduGuideBot 2025
# Weight scale: 0 = no influence, 5 = strong influence
# For each QUIZ answer, we list how it affects major/university attributes.

location:
  phnom_penh:
    campus_city=PP: 5
    campus_city=SR|BB: 0
    delivery_mode=online: 3          # online okay if no PP campus
  siem_reap:
    campus_city=SR: 5
    campus_city=PP|BB: 0
    delivery_mode=online: 3
  battambang:
    campus_city=BB: 5
    campus_city=PP|SR: 0
    delivery_mode=online: 3
  online:
    delivery_mode=online: 5
    campus_city=PP|SR|BB: 2          # hybrid fallback

budget:
  low:
    tuition_bracket=low:   5
    tuition_bracket=mid:   2
    tuition_bracket=high:  0
  mid:
    tuition_bracket=low:   2
    tuition_bracket=mid:   5
    tuition_bracket=high:  2
  high:
    tuition_bracket=high:  5
    tuition_bracket=mid:   3
    tuition_bracket=low:   1

learning_mode:
  traditional:
    delivery_mode=on_campus: 5
    delivery_mode=blended:   3
    delivery_mode=online:    1
  blended:
    delivery_mode=blended:   5
    delivery_mode=on_campus: 3
    delivery_mode=online:    3
  online_only:
    delivery_mode=online:    5
    delivery_mode=blended:   3
    delivery_mode=on_campus: 0

interest_field:
  stem:         { field_tag=STEM: 5 }
  arts:         { field_tag=ARTS: 5 }
  business:     { field_tag=BUS:  5 }
  agro_env:     { field_tag=AGRI: 5 }
  health:       { field_tag=HLTH: 5 }

career_goal:
  digital_tech: { career_cluster=TECH: 5 }
  engineering:  { career_cluster=ENGR: 5 }
  finance:      { career_cluster=FIN:  5 }
  education:    { career_cluster=EDU:  5 }
  unsure:       { }  # no boost

language_level:
  none:
    lang_req=none: 5
    lang_req=basic: 3
    lang_req=high: 0
  medium:
    lang_req=none|basic: 4
    lang_req=high: 2
  high:
    lang_req=high: 5
    lang_req=basic: 3
    lang_req=none: 2

strength_profile:
  logic_math: { strength_match=logic_math: 5 }
  balanced:   { strength_match=balanced:   5 }
  creative:   { strength_match=creative:  5 }

need_internship:
  yes:   { has_internship=True: 5 }
  maybe: { has_internship=True: 3 }
  no:    { }

scholarship:
  need:   { has_scholarship=True: 5 }
  maybe:  { has_scholarship=True: 3 }
  none:   { }

doc_support:
  self_prepare: { doc_support_level=basic: 0 }
  need_help:    { doc_support_level=guidance: 4 }

study_time:
  daytime:   { schedule=day: 5 }
  evening:   { schedule=evening: 5 }
  flexible:  { schedule=day|evening: 3 }

internet_access:
  stable:       { delivery_mode=online: 2 }
  mobile_only:  { delivery_mode=online: 4 }  # needs mobile-friendly LMS
  weak:         { delivery_mode=online: 0 }

green_value:
  high:   { campus_green=True: 5 }
  medium: { campus_green=True: 2 }
  low:    { }

work_abroad:
  local:  { intl_accredit=False: 5 }
  asean:  { intl_accredit|intl_partner=True: 4 }
  global: { intl_accredit=True: 5 }
