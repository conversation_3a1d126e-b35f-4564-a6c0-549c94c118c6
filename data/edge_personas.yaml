# Edge Case Test Personas for MCDA Regression Testing
# These scenarios test extreme combinations and edge cases

test_personas:
  - id: extreme_low_budget_it_online
    description: "Low budget student wanting IT career with online learning"
    answers:
      location: online
      budget: low
      learning_mode: online_only
      interest_field: stem
      career_goal: digital_tech
      language_level: high
      strength_profile: logic_math
      need_internship: yes
      scholarship: need
      doc_support: self_prepare
      study_time: flexible
      internet_access: stable
      green_value: low
      work_abroad: global
    expected_top_field: STEM
    must_not_recommend:
      - tuition_bracket=high
    min_recommendations: 3

  - id: pp_premium_business_on_campus
    description: "High budget business student preferring traditional campus"
    answers:
      location: phnom_penh
      budget: high
      learning_mode: traditional
      interest_field: business
      career_goal: finance
      language_level: medium
      strength_profile: balanced
      need_internship: maybe
      scholarship: none
      doc_support: need_help
      study_time: daytime
      internet_access: stable
      green_value: medium
      work_abroad: asean
    expected_uni_city: PP
    expected_top_field: BUS
    min_recommendations: 3

  - id: battambang_agriculture_evening
    description: "Rural student interested in agriculture with evening classes"
    answers:
      location: battambang
      budget: low
      learning_mode: blended
      interest_field: agro_env
      career_goal: unsure
      language_level: none
      strength_profile: balanced
      need_internship: yes
      scholarship: need
      doc_support: need_help
      study_time: evening
      internet_access: weak
      green_value: high
      work_abroad: local
    expected_uni_city: BB
    expected_top_field: AGRI
    must_not_recommend:
      - delivery_mode=online
      - tuition_bracket=high
    min_recommendations: 2

  - id: siem_reap_arts_creative
    description: "Creative student in Siem Reap interested in arts"
    answers:
      location: siem_reap
      budget: mid
      learning_mode: traditional
      interest_field: arts
      career_goal: unsure
      language_level: medium
      strength_profile: creative
      need_internship: maybe
      scholarship: maybe
      doc_support: self_prepare
      study_time: daytime
      internet_access: stable
      green_value: high
      work_abroad: local
    expected_uni_city: SR
    expected_top_field: ARTS
    min_recommendations: 3

  - id: stem_introvert_scholarship_no_internship
    description: "STEM student who needs scholarship but avoids internships"
    answers:
      location: phnom_penh
      budget: low
      learning_mode: online_only
      interest_field: stem
      career_goal: digital_tech
      language_level: high
      strength_profile: logic_math
      need_internship: no
      scholarship: need
      doc_support: self_prepare
      study_time: flexible
      internet_access: stable
      green_value: low
      work_abroad: global
    expected_top_field: STEM
    must_have_attributes:
      - has_scholarship=True
    must_not_recommend:
      - tuition_bracket=high
    min_recommendations: 3
