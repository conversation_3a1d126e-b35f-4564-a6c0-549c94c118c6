# 🚀 WORLD'S MOST ADVANCED EDUCATIONAL AI SYSTEM - DEPENDENCIES
# Core Essential Dependencies for Revolutionary EduGuide Bot 2025

# Core Framework & Bot
python-telegram-bot==21.0.1
aiofiles==23.2.1

# Essential AI & Machine Learning
torch==2.1.2
transformers==4.36.2
sentence-transformers==2.2.2
scikit-learn==1.3.2
numpy==1.24.4
pandas==2.1.4

# Essential NLP & Language Processing
nltk==3.8.1
langdetect==1.0.9

# Database & Caching
sqlalchemy==2.0.23

# Data Processing & Analytics
scipy==1.11.4

# Web & API
requests==2.31.0
aiohttp==3.9.1

# PDF & Document Generation
reportlab==4.0.7
jinja2==3.1.2

# Image Processing
Pillow==10.1.0

# Utilities & Performance
python-dotenv==1.0.0
loguru==0.7.2
rich==13.7.0
tqdm==4.66.1

# Security
cryptography>=3.4.8

# Advanced Features
python-dateutil==2.8.2
pytz==2023.3
