# 📅 Week 2 Backlog - Tighten-the-Bolts Edition

## 🎯 **MAINTENANCE-FIRST APPROACH**

Following the "speed-without-slop" philosophy, we're prioritizing maintenance tasks that compound long-term velocity alongside feature development.

---

## **📋 DAILY BREAKDOWN**

### **Tuesday - Consolidation & Data Enhancement**

#### **Feature Work (2-3 hours):**
- ✅ Enhanced scholarship filtering UX
- ✅ ROI calculation integration with real data
- ✅ Advanced budget optimization logic

#### **Maintenance Work (1-2 hours):**
- ✅ **COMPLETED**: Merge duplicate bot files → `archive/`
- ✅ **COMPLETED**: Single canonical entry point (`src/bot/app.py`)
- ✅ **COMPLETED**: <PERSON><PERSON><PERSON><PERSON><PERSON> logging toggle via environment
- ✅ **COMPLETED**: ROI reference CSV with data sources

### **Wednesday - Testing & Performance**

#### **Feature Work (2-3 hours):**
- 🔄 Import salary CSV + enhanced ROI calculations
- 🔄 Advanced filtering (deadlines, compare features)
- 🔄 UI/UX polish and mobile optimization

#### **Maintenance Work (1-2 hours):**
- ✅ **COMPLETED**: Property-based tests with Hypothesis
- 🔄 Performance optimization with vectorized scoring
- 🔄 Asset building optimization (SQLite/pickle)

### **Thursday - Quality & Resilience**

#### **Feature Work (2-3 hours):**
- 🔄 Enhanced explanation generation
- 🔄 Comparison features between universities
- 🔄 Advanced persona testing scenarios

#### **Maintenance Work (1-2 hours):**
- ✅ **COMPLETED**: CLI demo script for presentation resilience
- 🔄 Pre-commit hooks setup
- 🔄 Documentation badge integration

### **Friday - Polish & Deployment**

#### **Feature Work (2-3 hours):**
- 🔄 Final UI polish and user experience refinement
- 🔄 Performance testing and optimization
- 🔄 Presentation materials preparation

#### **Maintenance Work (1-2 hours):**
- 🔄 Clean university JSONs into optimized DB structure
- 🔄 Production deployment preparation
- 🔄 Final quality assurance and testing

---

## **✅ COMPLETED MAINTENANCE WINS**

### **1. Single Bot Entry Point**
- ✅ Created canonical `src/bot/app.py`
- ✅ Archived legacy bot files with clear documentation
- ✅ Updated Makefile to use canonical entry point
- ✅ Clear warning messages about deprecated files

**Impact**: Eliminates confusion about which file to run, simplifies testing and deployment.

### **2. Environment-Aware Logging**
- ✅ Rich logging for development (beautiful tracebacks)
- ✅ JSON logging for production (structured logs)
- ✅ Environment toggle via `USE_RICH_LOGGING` / `ENVIRONMENT`

**Impact**: Better debugging in dev, proper logging in production.

### **3. ROI Data Foundation**
- ✅ Created `data/roi_reference.csv` with source attribution
- ✅ Structured salary/employment data by field
- ✅ Source tracking for committee questions

**Impact**: Defensible salary numbers with clear data provenance.

### **4. Property-Based Testing**
- ✅ Hypothesis integration for 1000+ random test scenarios
- ✅ Invariant testing (scores non-negative, sorted results, etc.)
- ✅ Edge case discovery through automated exploration

**Impact**: Catches bugs that manual test personas miss, ensures system robustness.

### **5. Presentation Resilience**
- ✅ CLI demo script (`demo_cli.py`) for offline presentations
- ✅ Same MCDA engine, no internet required
- ✅ Complete Khmer interface for university demo

**Impact**: Guaranteed demo capability even if Telegram/internet fails.

### **6. Enhanced Documentation**
- ✅ Professional README with status badges
- ✅ Clear project structure documentation
- ✅ Honest capabilities and limitations
- ✅ Quick start and development guides

**Impact**: Professional presentation, clear onboarding for future developers.

---

## **🔄 REMAINING MAINTENANCE TASKS**

### **High Impact, Low Effort:**

#### **1. Pre-commit Hooks Setup**
```bash
# Install pre-commit
pip install pre-commit

# Setup hooks
pre-commit install

# Configure .pre-commit-config.yaml
```
**Benefit**: Automatic code formatting, prevents style/quality issues.

#### **2. Performance Optimization**
```python
# Vectorized MCDA scoring with numpy
weights_matrix = np.array(...)  # Pre-computed weight vectors
scores = weights_matrix @ user_answers_vector  # Single dot product
```
**Benefit**: 10x faster recommendation generation for high-volume usage.

#### **3. Asset Building Enhancement**
```python
# Convert JSON → SQLite → pickle for fast loading
def build_university_db():
    # Load all JSONs into SQLite
    # Run data validation queries
    # Export optimized pickle for runtime
```
**Benefit**: Faster startup, data validation, easier maintenance.

---

## **📊 WEEK 2 SUCCESS METRICS**

### **Feature Completion:**
- [ ] Enhanced scholarship filtering
- [ ] ROI calculation integration
- [ ] Advanced comparison features
- [ ] UI/UX polish completion
- [ ] Performance optimization

### **Maintenance Quality:**
- [x] Single canonical entry point
- [x] Environment-aware logging
- [x] Property-based testing
- [x] Presentation resilience
- [x] Professional documentation
- [ ] Pre-commit hooks
- [ ] Performance optimization
- [ ] Asset building enhancement

### **Technical Debt Reduction:**
- [x] File proliferation eliminated
- [x] Logging inconsistencies resolved
- [x] Test coverage gaps filled
- [x] Documentation gaps closed
- [ ] Performance bottlenecks addressed
- [ ] Data loading optimization

---

## **🎯 PRIORITY RECOMMENDATIONS**

### **Focus Areas for Remaining Week 2:**

1. **Complete ROI Integration** - Use the CSV data for real calculations
2. **Performance Optimization** - Vectorized scoring for speed
3. **Pre-commit Setup** - Automated quality gates
4. **Asset Building** - SQLite optimization for data loading
5. **Final Polish** - UI/UX refinement for presentation

### **Maintenance Philosophy:**
- **Each task < 1 hour** but compounds long-term velocity
- **Quality gates prevent regression** 
- **Speed optimizations pay dividends**
- **Documentation prevents future confusion**

---

## **🚀 READY FOR WEEK 3**

With these maintenance foundations in place, Week 3 development will be:
- **Faster** - No time wasted on tooling issues
- **Cleaner** - Automated quality enforcement
- **More Reliable** - Comprehensive testing coverage
- **Better Documented** - Clear architecture and capabilities

**The "tighten-the-bolts" approach is paying dividends!**
