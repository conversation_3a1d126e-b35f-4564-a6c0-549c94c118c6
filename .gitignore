# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# Environment variables (SECURITY - NEVER COMMIT TOKENS!)
.env
.env.local
.env.production
.env.staging
.env.development

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Project specific
*.log
*.hash
*.pkl
.pytest_cache/

# Legacy bot files (archived)
eduguide_master.py
eduguide_enhanced.py
eduguide_final.py
eduguide_bot_2025.py
correct_bot.py
main_bot.py
bot_server.py
eduguide_hybrid.py
