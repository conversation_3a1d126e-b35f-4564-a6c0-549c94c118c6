#!/usr/bin/env python3
"""
SIMPLE BOT TEST - Let's test step by step what actually works
"""

import json
import logging
from eduguide_bot_2025 import AdvancedMLEngine, ASSESSMENT_QUESTIONS

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_ml_engine():
    """Test if ML engine works"""
    print("🔧 Testing ML Engine...")
    try:
        ml = AdvancedMLEngine()
        df = ml.load_and_prepare_data()
        print(f"✅ Data loaded: {len(df)} majors")
        
        mse, r2 = ml.train_recommendation_model()
        print(f"✅ ML trained: R²={r2:.4f}")
        
        # Test recommendations
        test_answers = {
            1: 'grade_b',      # GPA
            2: 'math',         # Strong subject
            13: 'budget_medium' # Budget
        }
        
        recs = ml.get_ml_recommendations(test_answers)
        print(f"✅ Generated {len(recs)} recommendations")
        
        if recs:
            print(f"✅ Top recommendation: {recs[0]['university_name']} - {recs[0]['major_name_en']}")
            print(f"   Score: {recs[0]['final_score']:.3f}")
            print(f"   Tuition: ${recs[0]['tuition_usd']:.0f}")
        
        return True, ml
        
    except Exception as e:
        print(f"❌ ML Engine failed: {e}")
        return False, None

def test_assessment_questions():
    """Test if our 16 questions are properly defined"""
    print("\n📋 Testing Assessment Questions...")
    
    try:
        total_questions = len(ASSESSMENT_QUESTIONS)
        print(f"✅ Total questions: {total_questions}")
        
        if total_questions != 16:
            print(f"❌ Expected 16 questions, got {total_questions}")
            return False
        
        # Check each question has required fields
        categories = {}
        for q_num, question in ASSESSMENT_QUESTIONS.items():
            required_fields = ['question_kh', 'question_en', 'category', 'options']
            for field in required_fields:
                if field not in question:
                    print(f"❌ Question {q_num} missing field: {field}")
                    return False
            
            category = question['category']
            categories[category] = categories.get(category, 0) + 1
            
            print(f"✅ Q{q_num}: {question['category']} - {len(question['options'])} options")
        
        print(f"✅ Categories: {categories}")
        return True
        
    except Exception as e:
        print(f"❌ Assessment questions failed: {e}")
        return False

def test_recommendation_explanation(ml_engine):
    """Test if explanation generation works"""
    print("\n💬 Testing Explanation Generation...")
    
    try:
        from eduguide_bot_2025 import ExplanationEngine
        
        # Get a sample recommendation
        test_answers = {1: 'grade_b', 2: 'math', 13: 'budget_medium'}
        recs = ml_engine.get_ml_recommendations(test_answers)
        
        if not recs:
            print("❌ No recommendations to test explanation")
            return False
        
        # Generate user profile
        user_profile = ml_engine._process_user_assessment(test_answers)
        
        # Generate explanation
        explanation_engine = ExplanationEngine()
        explanation = explanation_engine.generate_explanation(recs[0], user_profile)
        
        print("✅ Explanation generated:")
        print(explanation[:200] + "..." if len(explanation) > 200 else explanation)
        
        return True
        
    except Exception as e:
        print(f"❌ Explanation generation failed: {e}")
        return False

def main():
    """Run all tests step by step"""
    print("🚀 HONEST BOT TESTING - STEP BY STEP")
    print("=" * 50)
    
    # Test 1: ML Engine
    ml_works, ml_engine = test_ml_engine()
    if not ml_works:
        print("❌ STOP: ML Engine doesn't work")
        return
    
    # Test 2: Assessment Questions
    questions_work = test_assessment_questions()
    if not questions_work:
        print("❌ STOP: Assessment questions have problems")
        return
    
    # Test 3: Explanation Generation
    if ml_engine:
        explanation_works = test_recommendation_explanation(ml_engine)
        if not explanation_works:
            print("❌ WARNING: Explanation generation has issues")
    
    print("\n" + "=" * 50)
    print("🎯 HONEST SUMMARY:")
    print(f"✅ ML Engine: {'WORKING' if ml_works else 'BROKEN'}")
    print(f"✅ Questions: {'WORKING' if questions_work else 'BROKEN'}")
    print(f"✅ Explanations: {'WORKING' if explanation_works else 'BROKEN'}")
    
    if ml_works and questions_work:
        print("\n🎉 CORE FUNCTIONALITY IS WORKING!")
        print("Next step: Test Telegram bot integration")
    else:
        print("\n❌ CORE ISSUES NEED FIXING FIRST")

if __name__ == "__main__":
    main()
