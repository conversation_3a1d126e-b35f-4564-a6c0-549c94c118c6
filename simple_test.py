# Simple test to identify execution issues
print("🔧 TESTING BASIC FUNCTIONALITY...")

# Test 1: Basic imports
try:
    import json
    print("✅ json imported")
except Exception as e:
    print(f"❌ json error: {e}")

try:
    import os
    print("✅ os imported")
except Exception as e:
    print(f"❌ os error: {e}")

# Test 2: File system access
try:
    files = os.listdir('eduguide_2025/Uni Data/PP')
    json_files = [f for f in files if f.endswith('.json')]
    print(f"✅ Found {len(json_files)} JSON files")
except Exception as e:
    print(f"❌ File access error: {e}")

# Test 3: Data loading
try:
    with open('eduguide_2025/Uni Data/PP/cadt-majors.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    majors = data.get('majors', [])
    print(f"✅ Loaded {len(majors)} majors from CADT")
except Exception as e:
    print(f"❌ Data loading error: {e}")

print("✅ BASIC TEST COMPLETE")
