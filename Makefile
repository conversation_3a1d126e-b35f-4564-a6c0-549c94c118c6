# EduGuideBot 2025 - <PERSON><PERSON> Ranker Makefile
# ================================================================

.PHONY: help assets train-ml test test-fast clean install bot cli

# Default target
help:
	@echo "🎓 EduGuideBot 2025 - ML Ranker Commands"
	@echo "========================================"
	@echo ""
	@echo "📦 Asset Management:"
	@echo "  assets     - Build programs parquet from JSON"
	@echo "  train-ml   - Train ML model on synthetic data"
	@echo "  clean      - Remove build artifacts"
	@echo ""
	@echo "🧪 Testing:"
	@echo "  test       - Run all tests"
	@echo "  test-fast  - Run smoke tests only"
	@echo ""
	@echo "🚀 Applications:"
	@echo "  bot        - Start Telegram bot"
	@echo "  cli        - Start CLI demo"
	@echo "  install    - Install dependencies"
	@echo ""

# Asset building
assets:
	@echo "🔧 Building assets..."
	python tools/build_assets.py
	@echo "✅ Assets built successfully"

# ML training
train-ml:
	@echo "🤖 Training ML model..."
	python -c "from src.core.ml_ranker import MLRanker; r = MLRanker(); r.train()"
	@echo "✅ ML model trained"

# Testing
test:
	@echo "🧪 Running all tests..."
	pytest tests/ -v
	@echo "✅ Tests completed"

test-fast:
	@echo "⚡ Running smoke tests..."
	pytest tests/test_smoke.py -v
	@echo "✅ Smoke tests passed"

# Applications
bot:
	@echo "🤖 Starting Telegram bot..."
	python src/bot/telegram_app.py

cli:
	@echo "💻 Starting CLI demo..."
	python src/bot/cli.py

# Development
install:
	@echo "📦 Installing dependencies..."
	pip install -r requirements.txt
	@echo "✅ Dependencies installed"

# Cleanup
clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -rf build/
	rm -rf .pytest_cache/
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	@echo "✅ Cleanup completed"
