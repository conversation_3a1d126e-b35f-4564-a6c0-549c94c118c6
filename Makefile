# EduGuideBot 2025 - Development Makefile
# Speed-without-slop automation

.PHONY: dev test test-fast lint format clean install build assets

# Development with hot-reload (canonical entry point)
dev:
	@echo "🚀 Starting bot with hot-reload..."
	watchfiles src/bot/app.py -- python src/bot/app.py

# Legacy bot (deprecated)
dev-legacy:
	@echo "⚠️ Using legacy bot (deprecated)..."
	watchfiles eduguide_master.py -- python eduguide_master.py

# CLI demo for presentations
demo:
	@echo "🎭 Starting CLI demo..."
	python demo_cli.py

# Sanity check before deployment
sanity:
	@echo "🔍 Running sanity check..."
	python sanity_check.py

# Setup pre-commit hooks
setup-hooks:
	@echo "🪝 Setting up pre-commit hooks..."
	pre-commit install
	@echo "✅ Pre-commit hooks installed"

# Fast parallel testing
test:
	@echo "🧪 Running full test suite..."
	pytest tests/ -v

test-fast:
	@echo "⚡ Running tests in parallel..."
	pytest tests/ -n auto -v

# Quick smoke test
test-quick:
	@echo "💨 Quick verification test..."
	python test_week1.py

# Coverage testing
coverage:
	@echo "📊 Running tests with coverage..."
	pytest tests/ --cov=src --cov-report=term-missing --cov-report=html
	@echo "📈 Coverage report generated in htmlcov/"

coverage-badge:
	@echo "🏷️ Generating coverage badge..."
	coverage run -m pytest tests/
	coverage report | tail -1 | awk '{print $$4}' | sed 's/%//' > coverage.txt
	@echo "📊 Coverage: $$(cat coverage.txt)%"

# Code quality
lint:
	@echo "🔍 Linting code..."
	flake8 src/ tests/ *.py --max-line-length=100 --ignore=E203,W503

format:
	@echo "✨ Formatting code..."
	black src/ tests/ *.py --line-length=100
	isort src/ tests/ *.py

# Asset building
assets:
	@echo "🔧 Building assets..."
	python tools/build_assets.py

# Dependency management
install:
	@echo "📦 Installing dependencies..."
	pip install -r requirements_master.txt

install-dev:
	@echo "📦 Installing dev dependencies..."
	pip install -r requirements_dev.txt

# Clean up
clean:
	@echo "🧹 Cleaning up..."
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete

# Build for deployment
build:
	@echo "🏗️ Building for deployment..."
	python -m compileall src/
	python tools/build_assets.py --production

# Full development setup
setup: install-dev assets setup-hooks
	@echo "✅ Development environment ready!"
	@echo "Run 'make dev' to start with hot-reload"

# Pre-deployment check
deploy-check: format lint test-fast sanity
	@echo "🚀 Ready for deployment!"

# CI pipeline simulation
ci: format lint test-fast
	@echo "✅ CI pipeline passed!"

# Help
help:
	@echo "EduGuideBot 2025 - Available Commands:"
	@echo "  dev          - Start bot with hot-reload"
	@echo "  demo         - Run CLI demo"
	@echo "  test         - Run full test suite"
	@echo "  test-fast    - Run tests in parallel"
	@echo "  test-quick   - Quick verification"
	@echo "  sanity       - Pre-deployment sanity check"
	@echo "  lint         - Check code quality"
	@echo "  format       - Format code"
	@echo "  assets       - Build YAML assets"
	@echo "  install      - Install dependencies"
	@echo "  setup-hooks  - Install pre-commit hooks"
	@echo "  clean        - Clean up cache files"
	@echo "  setup        - Full dev environment setup"
	@echo "  deploy-check - Pre-deployment validation"
	@echo "  ci           - Simulate CI pipeline"
