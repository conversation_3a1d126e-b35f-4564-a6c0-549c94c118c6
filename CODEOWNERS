# EduGuideBot 2025 - Code Ownership Rules
# Prevents accidental commits to wrong locations

# SECURITY: Environment files require review
.env* @eduguidebot/security-team
*.env @eduguidebot/security-team

# ARCHITECTURE: Root-level Python files are DEPRECATED
# Only src/bot/app.py should be the entry point
/*.py @eduguidebot/architecture-team

# CANONICAL ENTRY POINT: Only this file should be modified for bot logic
src/bot/app.py @eduguidebot/core-team

# DATA: University data changes need validation
eduguide_2025/Uni\ Data/ @eduguidebot/data-team
data/ @eduguidebot/data-team

# TESTING: Test changes need review
tests/ @eduguidebot/qa-team

# INFRASTRUCTURE: Build and deployment files
tools/ @eduguidebot/devops-team
Makefile @eduguidebot/devops-team
bootstrap.sh @eduguidebot/devops-team
requirements*.txt @eduguidebot/devops-team

# DOCUMENTATION: Docs need review for accuracy
*.md @eduguidebot/docs-team
docs/ @eduguidebot/docs-team

# Default: All other changes need at least one review
* @eduguidebot/core-team
