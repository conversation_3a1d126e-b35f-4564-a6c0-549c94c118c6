#!/usr/bin/env python3
"""
MCDA FLAT-LINE DEBUGGING SCRIPT
================================================================
Comprehensive analysis of MCDA scoring issues following your debugging framework
"""

import sys
sys.path.insert(0, 'src')

from mcda_recommender import MCDARecommendationEngine
import json
from pathlib import Path

def main():
    print("🔬 MCDA FLAT-LINE DEBUGGING ANALYSIS")
    print("=" * 60)
    
    # Test 1: Check data loading
    print("\n1️⃣ DATA LOADING TEST")
    print("-" * 30)
    
    data_dir = Path("eduguide_2025/Uni Data")
    if not data_dir.exists():
        print("❌ Data directory not found!")
        return
    
    json_count = 0
    for city_dir in data_dir.iterdir():
        if city_dir.is_dir():
            json_files = list(city_dir.glob("*.json"))
            json_count += len(json_files)
            print(f"📍 {city_dir.name}: {len(json_files)} JSON files")
    
    print(f"📊 Total JSON files: {json_count}")
    
    # Test 2: MCDA Engine Initialization
    print("\n2️⃣ MCDA ENGINE INITIALIZATION")
    print("-" * 30)
    
    try:
        engine = MCDARecommendationEngine()
        print(f"✅ Engine ready: {engine.is_ready}")
        print(f"📊 Programs loaded: {len(engine.programs)}")
        print(f"🎯 Weight matrix keys: {list(engine.weights_matrix.keys())}")
        
        if len(engine.programs) == 0:
            print("❌ No programs loaded - checking data structure...")
            
            # Sample one JSON file
            sample_file = next(data_dir.glob("*/*.json"))
            with open(sample_file, 'r', encoding='utf-8') as f:
                sample_data = json.load(f)
            
            print(f"📄 Sample file: {sample_file.name}")
            print(f"🔍 Keys: {list(sample_data.keys())}")
            if 'majors' in sample_data:
                print(f"📚 Majors count: {len(sample_data['majors'])}")
            return
        
    except Exception as e:
        print(f"❌ Engine initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Test 3: Program Attribute Analysis
    print("\n3️⃣ PROGRAM ATTRIBUTE ANALYSIS")
    print("-" * 30)
    
    sample_programs = engine.programs[:5]
    for i, p in enumerate(sample_programs, 1):
        print(f"\n{i}. {p.university_name[:30]}")
        print(f"   📚 Major: {p.major_name[:40]}")
        print(f"   📍 Campus: {p.campus_city}")
        print(f"   🏷️  Field: {p.field_tag}")
        print(f"   💰 Tuition: {p.tuition_bracket}")
        print(f"   📡 Delivery: {p.delivery_mode}")
        print(f"   🎯 Career: {p.career_cluster}")
        print(f"   📊 ROI: {p.roi_score:.2f}")
    
    # Test 4: Weight Matrix Analysis
    print("\n4️⃣ WEIGHT MATRIX ANALYSIS")
    print("-" * 30)
    
    for question, weights in list(engine.weights_matrix.items())[:3]:
        print(f"\n📋 {question}:")
        for answer, rules in weights.items():
            print(f"   {answer}: {rules}")
    
    # Test 5: MCDA Scoring Test (THE CRITICAL TEST)
    print("\n5️⃣ MCDA SCORING TEST - FLAT-LINE DETECTION")
    print("-" * 30)
    
    test_answers = {
        'location': 'phnom_penh',
        'budget': 'mid',
        'interest_field': 'stem',
        'career_goal': 'digital_tech'
    }
    
    print(f"🎯 Test answers: {test_answers}")
    
    # This will trigger our debug injection in get_recommendations
    recommendations = engine.get_recommendations(test_answers, limit=5)
    
    print(f"\n📊 Final recommendations: {len(recommendations)}")
    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec.program.university_name[:25]} - {rec.program.major_name[:30]}")
        print(f"   Score: {rec.total_score:.3f} | Match: {rec.match_percentage:.1f}%")
        print(f"   Breakdown: {rec.score_breakdown}")
    
    # Test 6: Attribute Matching Verification
    print("\n6️⃣ ATTRIBUTE MATCHING VERIFICATION")
    print("-" * 30)
    
    sample_program = engine.programs[0]
    print(f"🔍 Testing program: {sample_program.major_name}")
    
    # Test each weight matrix condition
    test_conditions = [
        f"campus_city={sample_program.campus_city}",
        f"field_tag={sample_program.field_tag}",
        f"tuition_bracket={sample_program.tuition_bracket}",
        f"delivery_mode={sample_program.delivery_mode}"
    ]
    
    for condition in test_conditions:
        matches = engine._program_matches_condition(sample_program, condition)
        print(f"   {condition}: {matches}")
    
    print("\n🎉 DEBUGGING COMPLETE!")
    print("Check the output above for flat-line issues and attribute mismatches.")

if __name__ == "__main__":
    main()
