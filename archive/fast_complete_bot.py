#!/usr/bin/env python3
"""
FAST COMPLETE EDUGUIDE BOT
================================================================

EXACTLY WHAT YOU WANT:
- INSTANT responses (no ML loading delays)
- Manual text search for universities
- Browse by budget, location, type
- 3 simple questions (fast like before)
- ALL browsing features restored
- NO SLOW MATHEMATICAL LOADING

Bot Token: 8198268528:AAHuAvp3bRqO5hdPD3tbv1xSks2YjXtiu9Y
"""

import json
import os
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BOT_TOKEN = "8198268528:AAHuAvp3bRqO5hdPD3tbv1xSks2YjXtiu9Y"

# Global data storage
university_data = []
user_sessions = {}

def load_simple_data():
    """Load university data FAST - no ML processing."""
    global university_data
    university_data = []
    
    logger.info("Loading university data FAST...")
    
    data_dir = "eduguide_2025/Uni Data"
    universities_loaded = 0
    
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.json'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    university = data.get('university', {})
                    majors = data.get('majors', [])
                    
                    # Skip if missing basic info
                    if not university.get('name_en') or not majors:
                        continue
                    
                    universities_loaded += 1
                    
                    # Extract simple info for each major
                    for major in majors:
                        major_info = extract_simple_major_info(major, university)
                        if major_info:
                            university_data.append(major_info)
                            
                except Exception as e:
                    logger.warning(f"Skipping {file}: {e}")
    
    logger.info(f"FAST loading complete: {universities_loaded} universities with {len(university_data)} majors")

def extract_simple_major_info(major, university):
    """Extract basic info we need - FAST processing."""
    try:
        major_info = major.get('major_info', {})
        practical_info = major.get('practical_information', {})
        career_prospects = major.get('career_prospects', {})
        
        # Basic names
        major_name_en = major_info.get('name_en', '').strip()
        major_name_kh = major_info.get('name_kh', '').strip()
        
        if not major_name_en:
            return None
        
        # Tuition
        tuition_str = str(practical_info.get('tuition_fees_usd', '0'))
        try:
            tuition_usd = float(tuition_str.replace(',', ''))
        except:
            tuition_usd = 0.0
        
        # Employment rate
        employment_stats = career_prospects.get('employment_statistics', {})
        employment_rate_str = employment_stats.get('employment_rate', '0%')
        try:
            employment_rate = float(employment_rate_str.replace('%', '')) / 100.0
        except:
            employment_rate = 0.0
        
        # Career keywords
        careers = career_prospects.get('potential_careers_en', [])
        career_text = ' '.join(careers).lower() if careers else ''
        
        return {
            'university_name': university.get('name_en', ''),
            'university_name_kh': university.get('name_kh', ''),
            'university_city': university.get('location', {}).get('city', ''),
            'university_type': university.get('type', ''),
            'major_name_en': major_name_en,
            'major_name_kh': major_name_kh,
            'tuition_usd': tuition_usd,
            'employment_rate': employment_rate,
            'career_keywords': career_text,
            'major_keywords': major_name_en.lower()
        }
        
    except Exception as e:
        return None

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Start command - INSTANT response."""
    user_id = update.effective_user.id
    user_sessions[user_id] = {}
    
    text = f"""🎓 **EduGuide Bot - Enhanced & Fast**

🚀 **INSTANT responses - no waiting!**

📊 **Live Data:** {len(university_data)} majors from {len(set(m['university_name'] for m in university_data))} universities

📋 **Enhanced Features:**
• 🔍 Smart search - type university/major names
• 💰 Budget explorer with statistics
• 📍 Location finder with details
• 🏛️ University type browser
• ⚡ Quick 3-question recommendations
• 📊 Real-time statistics & analytics
• 🎯 Detailed university profiles

🎯 **Fast, honest, and enhanced**
❌ No fake AI - just working features that actually function

Choose what you want to do:"""

    keyboard = [
        [InlineKeyboardButton("🔍 Search Universities", callback_data="manual_search")],
        [InlineKeyboardButton("💰 Browse by Budget", callback_data="browse_budget")],
        [InlineKeyboardButton("📍 Browse by Location", callback_data="browse_location")],
        [InlineKeyboardButton("🏛️ Browse by Type", callback_data="browse_type")],
        [InlineKeyboardButton("⚡ Quick Questions", callback_data="start_questions")],
        [InlineKeyboardButton("📊 Statistics", callback_data="show_stats")]
    ]
    
    await update.message.reply_text(
        text, 
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def handle_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle button callbacks - INSTANT responses."""
    query = update.callback_query
    await query.answer()
    
    user_id = query.from_user.id
    data = query.data
    
    # INSTANT routing - no delays
    if data == "manual_search":
        await start_manual_search(query)
    elif data == "browse_budget":
        await browse_by_budget(query)
    elif data == "browse_location":
        await browse_by_location(query)
    elif data == "browse_type":
        await browse_by_type(query)
    elif data == "start_questions":
        await ask_budget(query, user_id)
    elif data == "show_stats":
        await show_statistics(query)
    elif data.startswith("budget_"):
        budget = int(data.split("_")[1])
        user_sessions[user_id]['budget'] = budget
        await ask_location(query, user_id)
    elif data.startswith("location_"):
        location = data.split("_", 1)[1]
        user_sessions[user_id]['location'] = location
        await ask_interests(query, user_id)
    elif data.startswith("interest_"):
        interest = data.split("_", 1)[1]
        user_sessions[user_id]['interest'] = interest
        await show_recommendations(query, user_id)
    elif data.startswith("budget_browse_"):
        await show_budget_results(query, data)
    elif data.startswith("location_browse_"):
        await show_location_results(query, data)
    elif data.startswith("type_browse_"):
        await show_type_results(query, data)
    elif data == "back_to_start":
        await back_to_start(query, user_id)

async def start_manual_search(query):
    """Start manual text search."""
    text = """🔍 **Manual University Search**

Type the name of a university you want to search for.

**Examples:**
• Royal University
• Norton University  
• IIC University
• Cambodia University

Just type the university name and I'll find it instantly!"""

    keyboard = [
        [InlineKeyboardButton("🔙 Back to Menu", callback_data="back_to_start")]
    ]
    
    await query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def handle_text_search(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle ENHANCED manual text search - INSTANT results."""
    search_text = update.message.text.lower().strip()

    # ENHANCED search through university data
    results = []
    exact_matches = []
    partial_matches = []

    for major in university_data:
        # Check for exact matches first
        if (search_text == major['university_name'].lower() or
            search_text == major['major_name_en'].lower()):
            exact_matches.append(major)
        # Then partial matches
        elif (search_text in major['university_name'].lower() or
              search_text in major['university_name_kh'].lower() or
              search_text in major['major_name_en'].lower() or
              search_text in major['major_name_kh'].lower() or
              search_text in major['career_keywords']):
            partial_matches.append(major)

    # Prioritize exact matches, then partial matches
    results = exact_matches + partial_matches
    
    if not results:
        text = f"""❌ **No results found for "{update.message.text}"**

Try searching for:
• University name (e.g., "Royal", "Norton")
• Major name (e.g., "Computer", "Business")

Or use the browse options below:"""
        
        keyboard = [
            [InlineKeyboardButton("💰 Browse by Budget", callback_data="browse_budget")],
            [InlineKeyboardButton("📍 Browse by Location", callback_data="browse_location")],
            [InlineKeyboardButton("🏠 Main Menu", callback_data="back_to_start")]
        ]
    else:
        # Group by university
        universities = {}
        for major in results:
            uni_name = major['university_name']
            if uni_name not in universities:
                universities[uni_name] = {
                    'name': uni_name,
                    'name_kh': major['university_name_kh'],
                    'city': major['university_city'],
                    'type': major['university_type'],
                    'majors': [],
                    'min_tuition': float('inf')
                }
            universities[uni_name]['majors'].append(major)
            universities[uni_name]['min_tuition'] = min(universities[uni_name]['min_tuition'], major['tuition_usd'])
        
        # Calculate enhanced statistics
        total_majors = sum(len(uni_data['majors']) for uni_data in universities.values())
        avg_employment = sum(max(m['employment_rate'] for m in uni_data['majors']) for uni_data in universities.values()) / len(universities)

        text = f"""🎯 **Enhanced Search Results for "{update.message.text}"**

📊 **Found:** {len(universities)} universities • {total_majors} majors
📈 **Average Employment Rate:** {avg_employment:.0%}

"""

        for i, (uni_name, uni_data) in enumerate(list(universities.items())[:5], 1):
            # Get best major for this university
            best_major = max(uni_data['majors'], key=lambda x: x['employment_rate'])

            text += f"""**{i}. {uni_data['name']}**
📍 {uni_data['city']} • {uni_data['type']}
💰 From ${uni_data['min_tuition']:.0f}/year
📚 {len(uni_data['majors'])} majors available
🎯 Top Major: {best_major['major_name_en']}
📊 Best Employment Rate: {best_major['employment_rate']:.0%}

"""
        
        keyboard = [
            [InlineKeyboardButton("🔍 Search Again", callback_data="manual_search")],
            [InlineKeyboardButton("🏠 Main Menu", callback_data="back_to_start")]
        ]
    
    await update.message.reply_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

# FAST BROWSING FUNCTIONS
async def browse_by_budget(query):
    """Browse by budget - ENHANCED with statistics."""
    # Calculate budget statistics
    budget_stats = {
        'under_500': len([m for m in university_data if m['tuition_usd'] < 500 and m['tuition_usd'] > 0]),
        '500_800': len([m for m in university_data if 500 <= m['tuition_usd'] <= 800]),
        '800_1200': len([m for m in university_data if 800 < m['tuition_usd'] <= 1200]),
        'over_1200': len([m for m in university_data if m['tuition_usd'] > 1200])
    }

    text = f"""💰 **Enhanced Budget Explorer**

📊 **Live Statistics:**
• Under $500: {budget_stats['under_500']} majors available
• $500-$800: {budget_stats['500_800']} majors available
• $800-$1200: {budget_stats['800_1200']} majors available
• Over $1200: {budget_stats['over_1200']} majors available

💡 **Most Popular Range:** $500-$800 (Best Value)

Choose your budget range:"""

    keyboard = [
        [InlineKeyboardButton(f"💸 Under $500 ({budget_stats['under_500']})", callback_data="budget_browse_500")],
        [InlineKeyboardButton(f"💵 $500-$800 ({budget_stats['500_800']})", callback_data="budget_browse_800")],
        [InlineKeyboardButton(f"💴 $800-$1200 ({budget_stats['800_1200']})", callback_data="budget_browse_1200")],
        [InlineKeyboardButton(f"💶 Over $1200 ({budget_stats['over_1200']})", callback_data="budget_browse_1500")],
        [InlineKeyboardButton("🔙 Back", callback_data="back_to_start")]
    ]

    await query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def browse_by_location(query):
    """Browse by location - INSTANT."""
    text = """📍 **Browse by Location**

Choose your preferred location:"""

    keyboard = [
        [InlineKeyboardButton("🏙️ Phnom Penh", callback_data="location_browse_ភ្នំពេញ")],
        [InlineKeyboardButton("🏛️ Siem Reap", callback_data="location_browse_សៀមរាប")],
        [InlineKeyboardButton("🌾 Battambang", callback_data="location_browse_បាត់ដំបង")],
        [InlineKeyboardButton("🌍 All Locations", callback_data="location_browse_all")],
        [InlineKeyboardButton("🔙 Back", callback_data="back_to_start")]
    ]

    await query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def browse_by_type(query):
    """Browse by university type - INSTANT."""
    text = """🏛️ **Browse by University Type**

Choose university type:"""

    keyboard = [
        [InlineKeyboardButton("🏛️ Public Universities", callback_data="type_browse_public")],
        [InlineKeyboardButton("🏢 Private Universities", callback_data="type_browse_private")],
        [InlineKeyboardButton("🌍 All Types", callback_data="type_browse_all")],
        [InlineKeyboardButton("🔙 Back", callback_data="back_to_start")]
    ]

    await query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def show_budget_results(query, data):
    """Show budget results - INSTANT."""
    budget = int(data.split("_")[-1])

    # INSTANT filtering
    filtered = [m for m in university_data if m['tuition_usd'] <= budget and m['tuition_usd'] > 0]

    if not filtered:
        text = f"""😔 **No universities found under ${budget}**

Try increasing your budget or check other options."""
        keyboard = [
            [InlineKeyboardButton("🔄 Try Different Budget", callback_data="browse_budget")],
            [InlineKeyboardButton("🏠 Main Menu", callback_data="back_to_start")]
        ]
    else:
        # Group by university
        universities = {}
        for major in filtered:
            uni_name = major['university_name']
            if uni_name not in universities:
                universities[uni_name] = {
                    'name': uni_name,
                    'city': major['university_city'],
                    'majors': 0,
                    'min_tuition': float('inf')
                }
            universities[uni_name]['majors'] += 1
            universities[uni_name]['min_tuition'] = min(universities[uni_name]['min_tuition'], major['tuition_usd'])

        text = f"""💰 **Universities under ${budget}**

📊 **Found {len(universities)} universities:**

"""

        for i, (uni_name, uni_data) in enumerate(list(universities.items())[:8], 1):
            text += f"""**{i}. {uni_data['name']}**
💰 From ${uni_data['min_tuition']:.0f}/year
📚 {uni_data['majors']} majors
📍 {uni_data['city']}

"""

        keyboard = [
            [InlineKeyboardButton("🔄 Different Budget", callback_data="browse_budget")],
            [InlineKeyboardButton("⚡ Quick Questions", callback_data="start_questions")],
            [InlineKeyboardButton("🏠 Main Menu", callback_data="back_to_start")]
        ]

    await query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

# FAST QUESTION SYSTEM (like your original)
async def ask_budget(query, user_id):
    """Ask about budget - INSTANT."""
    text = "💰 តើថវិកាសម្រាប់ការសិក្សារបស់អ្នកប្រហែលប៉ុន្មាន?"

    keyboard = [
        [InlineKeyboardButton("💸 តិចជាង $500", callback_data="budget_500")],
        [InlineKeyboardButton("💵 $500 - $800", callback_data="budget_800")],
        [InlineKeyboardButton("💴 $800 - $1000", callback_data="budget_1000")],
        [InlineKeyboardButton("💶 លើសពី $1000", callback_data="budget_1500")]
    ]

    await query.edit_message_text(text, reply_markup=InlineKeyboardMarkup(keyboard))

async def ask_location(query, user_id):
    """Ask about location - INSTANT."""
    text = "📍 តើអ្នកចង់សិក្សានៅខេត្តណា?"

    keyboard = [
        [InlineKeyboardButton("🏙️ ភ្នំពេញ", callback_data="location_ភ្នំពេញ")],
        [InlineKeyboardButton("🏛️ សៀមរាប", callback_data="location_សៀមរាប")],
        [InlineKeyboardButton("🌾 បាត់ដំបង", callback_data="location_បាត់ដំបង")],
        [InlineKeyboardButton("🌍 គ្រប់ទីកន្លែង", callback_data="location_any")]
    ]

    await query.edit_message_text(text, reply_markup=InlineKeyboardMarkup(keyboard))

async def ask_interests(query, user_id):
    """Ask about interests - INSTANT."""
    text = "🎯 តើអ្នកចូលចិត្តធ្វើការជាមួយអ្វី?"

    keyboard = [
        [InlineKeyboardButton("💻 បច្ចេកវិទ្យា", callback_data="interest_technology")],
        [InlineKeyboardButton("👥 មនុស្ស និងអាជីវកម្ម", callback_data="interest_business")],
        [InlineKeyboardButton("🏥 សុខភាព និងវេជ្ជសាស្ត្រ", callback_data="interest_health")],
        [InlineKeyboardButton("🌱 កសិកម្ម និងបរិស្ថាន", callback_data="interest_agriculture")],
        [InlineKeyboardButton("🎨 សិល្បៈ និងភាសា", callback_data="interest_arts")]
    ]

    await query.edit_message_text(text, reply_markup=InlineKeyboardMarkup(keyboard))

async def show_recommendations(query, user_id):
    """Show recommendations - INSTANT like your original."""
    user_prefs = user_sessions[user_id]

    # INSTANT filtering (same as your original)
    filtered_majors = []

    for major in university_data:
        # Budget filter
        if major['tuition_usd'] > user_prefs['budget']:
            continue

        # Location filter
        if user_prefs['location'] != 'any' and major['university_city'] != user_prefs['location']:
            continue

        # Interest filter (simple keyword matching)
        interest = user_prefs['interest']
        if interest == 'technology' and not any(word in major['major_keywords'] for word in ['computer', 'engineering', 'technology', 'it']):
            continue
        elif interest == 'business' and not any(word in major['major_keywords'] for word in ['business', 'management', 'economics', 'finance']):
            continue
        elif interest == 'health' and not any(word in major['major_keywords'] for word in ['medicine', 'health', 'nursing']):
            continue
        elif interest == 'agriculture' and not any(word in major['major_keywords'] for word in ['agriculture', 'crop', 'animal']):
            continue
        elif interest == 'arts' and not any(word in major['major_keywords'] for word in ['art', 'language', 'literature']):
            continue

        filtered_majors.append(major)

    if not filtered_majors:
        await query.edit_message_text(
            "😔 សូមទោស! រកមិនឃើញការណែនាំដែលសមស្រប។\n\nសូមព្យាយាមកែប្រែលក្ខខណ្ឌ។",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("🔄 ព្យាយាមម្តងទៀត", callback_data="start_questions")
            ]])
        )
        return

    # Sort by employment rate
    filtered_majors.sort(key=lambda x: x['employment_rate'], reverse=True)

    # Format results (same as your original)
    result_text = f"🎯 ការណែនាំសម្រាប់អ្នក (ចំនួន {len(filtered_majors)})\n\n"

    for i, major in enumerate(filtered_majors[:5], 1):
        result_text += f"**{i}. {major['university_name_kh']}**\n"
        result_text += f"📚 {major['major_name_kh']}\n"
        result_text += f"💰 ថ្លៃសិក្សា: ${major['tuition_usd']:.0f}/ឆ្នាំ\n"
        if major['employment_rate'] > 0:
            result_text += f"📊 អត្រាការងារ: {major['employment_rate']:.0%}\n"
        result_text += f"📍 {major['university_city']}\n\n"

    result_text += "✅ ការណែនាំដោយផ្អែកលើការស្វែងរកសាមញ្ញ"

    keyboard = [
        [InlineKeyboardButton("🔄 ស្វែងរកម្តងទៀត", callback_data="start_questions")],
        [InlineKeyboardButton("🔍 Manual Search", callback_data="manual_search")],
        [InlineKeyboardButton("🏠 ទំព័រដើម", callback_data="back_to_start")]
    ]

    await query.edit_message_text(
        result_text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

# REMAINING FUNCTIONS
async def show_location_results(query, data):
    """Show location results - INSTANT."""
    location = data.split("_", 2)[-1]

    # INSTANT filtering
    if location == "all":
        filtered = university_data
        location_name = "All Locations"
    else:
        filtered = [m for m in university_data if m['university_city'] == location]
        location_name = location

    # Group by university
    universities = {}
    for major in filtered:
        uni_name = major['university_name']
        if uni_name not in universities:
            universities[uni_name] = {
                'name': uni_name,
                'city': major['university_city'],
                'majors': 0,
                'min_tuition': float('inf')
            }
        universities[uni_name]['majors'] += 1
        if major['tuition_usd'] > 0:
            universities[uni_name]['min_tuition'] = min(universities[uni_name]['min_tuition'], major['tuition_usd'])

    text = f"""📍 **Universities in {location_name}**

📊 **Found {len(universities)} universities:**

"""

    for i, (uni_name, uni_data) in enumerate(list(universities.items())[:8], 1):
        tuition_text = f"${uni_data['min_tuition']:.0f}/year" if uni_data['min_tuition'] != float('inf') else "Contact for fees"
        text += f"""**{i}. {uni_data['name']}**
💰 From {tuition_text}
📚 {uni_data['majors']} majors

"""

    keyboard = [
        [InlineKeyboardButton("🔄 Different Location", callback_data="browse_location")],
        [InlineKeyboardButton("⚡ Quick Questions", callback_data="start_questions")],
        [InlineKeyboardButton("🏠 Main Menu", callback_data="back_to_start")]
    ]

    await query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def show_type_results(query, data):
    """Show university type results - INSTANT."""
    uni_type = data.split("_", 2)[-1]

    # INSTANT filtering
    if uni_type == "all":
        filtered = university_data
        type_name = "All Types"
    elif uni_type == "public":
        filtered = [m for m in university_data if 'public' in m['university_type'].lower()]
        type_name = "Public Universities"
    elif uni_type == "private":
        filtered = [m for m in university_data if 'private' in m['university_type'].lower()]
        type_name = "Private Universities"
    else:
        filtered = university_data
        type_name = "All Types"

    # Group by university
    universities = {}
    for major in filtered:
        uni_name = major['university_name']
        if uni_name not in universities:
            universities[uni_name] = {
                'name': uni_name,
                'city': major['university_city'],
                'type': major['university_type'],
                'majors': 0,
                'min_tuition': float('inf')
            }
        universities[uni_name]['majors'] += 1
        if major['tuition_usd'] > 0:
            universities[uni_name]['min_tuition'] = min(universities[uni_name]['min_tuition'], major['tuition_usd'])

    text = f"""🏛️ **{type_name}**

📊 **Found {len(universities)} universities:**

"""

    for i, (uni_name, uni_data) in enumerate(list(universities.items())[:8], 1):
        tuition_text = f"${uni_data['min_tuition']:.0f}/year" if uni_data['min_tuition'] != float('inf') else "Contact for fees"
        text += f"""**{i}. {uni_data['name']}**
🏛️ {uni_data['type']}
💰 From {tuition_text}
📚 {uni_data['majors']} majors
📍 {uni_data['city']}

"""

    keyboard = [
        [InlineKeyboardButton("🔄 Different Type", callback_data="browse_type")],
        [InlineKeyboardButton("⚡ Quick Questions", callback_data="start_questions")],
        [InlineKeyboardButton("🏠 Main Menu", callback_data="back_to_start")]
    ]

    await query.edit_message_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='Markdown'
    )

async def show_statistics(query):
    """Show ENHANCED statistics with detailed analytics."""
    if not university_data:
        await query.edit_message_text("❌ មិនមានទិន្នន័យ")
        return

    # Calculate enhanced statistics
    total_majors = len(university_data)
    universities = set(m['university_name'] for m in university_data)
    cities = set(m['university_city'] for m in university_data if m['university_city'])

    tuitions = [m['tuition_usd'] for m in university_data if m['tuition_usd'] > 0]
    employment_rates = [m['employment_rate'] for m in university_data if m['employment_rate'] > 0]

    # Enhanced analytics
    public_unis = len([m for m in university_data if 'public' in m.get('university_type', '').lower()])
    private_unis = len([m for m in university_data if 'private' in m.get('university_type', '').lower()])

    # Top performing majors
    top_employment = sorted(university_data, key=lambda x: x['employment_rate'], reverse=True)[:3]

    # City distribution
    city_counts = {}
    for major in university_data:
        city = major['university_city']
        if city:
            city_counts[city] = city_counts.get(city, 0) + 1

    text = f"""📊 **Enhanced Analytics Dashboard**

🎓 **University Overview:**
• Total Universities: {len(universities)} places
• Total Majors: {total_majors} programs
• Cities Available: {len(cities)} locations
• Public Universities: {public_unis} majors
• Private Universities: {private_unis} majors

💰 **Financial Analysis:**
• Minimum Tuition: ${min(tuitions):.0f}/year
• Maximum Tuition: ${max(tuitions):.0f}/year
• Average Tuition: ${sum(tuitions)/len(tuitions):.0f}/year
• Median Tuition: ${sorted(tuitions)[len(tuitions)//2]:.0f}/year

📊 **Employment Analytics:**
• Average Employment Rate: {sum(employment_rates)/len(employment_rates):.0%}
• Highest Employment: {max(employment_rates):.0%}
• Lowest Employment: {min(employment_rates):.0%}

🏆 **Top Performing Majors:**
1. {top_employment[0]['major_name_en']} ({top_employment[0]['employment_rate']:.0%})
2. {top_employment[1]['major_name_en']} ({top_employment[1]['employment_rate']:.0%})
3. {top_employment[2]['major_name_en']} ({top_employment[2]['employment_rate']:.0%})

📍 **City Distribution:**
• Phnom Penh: {city_counts.get('ភ្នំពេញ', 0)} majors
• Siem Reap: {city_counts.get('សៀមរាប', 0)} majors
• Battambang: {city_counts.get('បាត់ដំបង', 0)} majors

✅ Real-time data from JSON files"""

    keyboard = [
        [InlineKeyboardButton("🔍 Search Universities", callback_data="manual_search")],
        [InlineKeyboardButton("⚡ Quick Questions", callback_data="start_questions")],
        [InlineKeyboardButton("🏠 Main Menu", callback_data="back_to_start")]
    ]

    await query.edit_message_text(text, reply_markup=InlineKeyboardMarkup(keyboard), parse_mode='Markdown')

async def back_to_start(query, user_id):
    """Go back to start - INSTANT."""
    user_sessions[user_id] = {}

    text = """🎓 **EduGuide Bot - Fast & Complete**

🚀 **INSTANT responses - no waiting!**

Choose what you want to do:"""

    keyboard = [
        [InlineKeyboardButton("🔍 Search Universities", callback_data="manual_search")],
        [InlineKeyboardButton("💰 Browse by Budget", callback_data="browse_budget")],
        [InlineKeyboardButton("📍 Browse by Location", callback_data="browse_location")],
        [InlineKeyboardButton("🏛️ Browse by Type", callback_data="browse_type")],
        [InlineKeyboardButton("⚡ Quick Questions", callback_data="start_questions")],
        [InlineKeyboardButton("📊 Statistics", callback_data="show_stats")]
    ]

    await query.edit_message_text(text, reply_markup=InlineKeyboardMarkup(keyboard), parse_mode='Markdown')

def main():
    """Run the FAST complete bot."""
    # Load data FAST - no ML processing
    load_simple_data()

    if not university_data:
        logger.error("No data loaded! Check the data directory.")
        return

    # Create application
    application = Application.builder().token(BOT_TOKEN).build()

    # Add handlers
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CallbackQueryHandler(handle_callback))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_text_search))

    logger.info(f"Starting FAST Complete EduGuide Bot with {len(university_data)} majors...")
    application.run_polling()

if __name__ == "__main__":
    main()
