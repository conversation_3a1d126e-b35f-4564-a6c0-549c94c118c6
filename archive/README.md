# 📁 Legacy Bot Files Archive

⚠️ **WARNING: These files are LEGACY/EXPERIMENTAL and should NOT be used.**

The canonical bot entry point is: `src/bot/app.py`

## Archived Files:

### Legacy Bot Implementations:
- `eduguide_master.py` - Previous master version
- `eduguide_enhanced.py` - Enhanced system version  
- `eduguide_final.py` - Final deployment version
- `eduguide_bot_2025.py` - ML version attempt
- `correct_bot.py` - Structure-corrected version
- `main_bot.py` - Main bot implementation
- `bot_server.py` - Server version
- `eduguide_hybrid.py` - Hybrid approach version

### Legacy Test Files:
- Various experimental test implementations
- Outdated verification scripts

### Why These Are Archived:
1. **Multiple entry points** caused confusion
2. **Inconsistent implementations** across files
3. **Maintenance burden** of keeping multiple versions
4. **Testing complexity** with unclear canonical version

## Migration Notes:
All functionality has been consolidated into `src/bot/app.py` with:
- ✅ Single canonical entry point
- ✅ Environment-aware logging (Rich dev / JSON prod)
- ✅ Clean architecture with proper imports
- ✅ Comprehensive error handling
- ✅ Production-ready configuration

**Use `src/bot/app.py` for all development and deployment.**
