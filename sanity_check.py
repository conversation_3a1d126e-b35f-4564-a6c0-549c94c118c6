#!/usr/bin/env python3
"""
EduGuideBot Sanity Check Script
================================================================

Quick verification that all systems are working before deployment.
Run this before any presentation or production deployment.

Usage: python sanity_check.py
"""

import sys
import os
from pathlib import Path
import subprocess
import time

def run_command(cmd: str, description: str) -> bool:
    """Run a command and return success status."""
    print(f"🔍 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print(f"✅ {description} - PASSED")
            return True
        else:
            print(f"❌ {description} - FAILED")
            print(f"   Error: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - TIMEOUT")
        return False
    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False

def check_environment() -> bool:
    """Check environment setup."""
    print("\n🔧 ENVIRONMENT CHECKS")
    print("=" * 40)
    
    checks = []
    
    # Check Python version
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✅ Python {python_version.major}.{python_version.minor} - OK")
        checks.append(True)
    else:
        print(f"❌ Python {python_version.major}.{python_version.minor} - Need 3.8+")
        checks.append(False)
    
    # Check critical files exist
    critical_files = [
        "src/bot/app.py",
        "src/mcda_recommender.py", 
        "data/weights_matrix.yaml",
        "demo_cli.py",
        ".env.example"
    ]
    
    for file_path in critical_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} - EXISTS")
            checks.append(True)
        else:
            print(f"❌ {file_path} - MISSING")
            checks.append(False)
    
    # Check .env file
    if Path(".env").exists():
        print("✅ .env file - EXISTS")
        checks.append(True)
    else:
        print("⚠️ .env file - MISSING (create from .env.example)")
        checks.append(False)
    
    # Check BOT_TOKEN
    bot_token = os.getenv("BOT_TOKEN")
    if bot_token:
        print("✅ BOT_TOKEN - SET")
        checks.append(True)
    else:
        print("❌ BOT_TOKEN - NOT SET")
        checks.append(False)
    
    return all(checks)

def check_dependencies() -> bool:
    """Check that all dependencies are installed."""
    print("\n📦 DEPENDENCY CHECKS")
    print("=" * 40)
    
    checks = []
    
    # Check core dependencies
    dependencies = [
        "telegram",
        "yaml", 
        "dotenv",
        "pytest"
    ]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} - INSTALLED")
            checks.append(True)
        except ImportError:
            print(f"❌ {dep} - MISSING")
            checks.append(False)
    
    return all(checks)

def check_build_assets() -> bool:
    """Check asset building."""
    print("\n🏗️ ASSET BUILD CHECKS")
    print("=" * 40)
    
    checks = []
    
    # Clean and rebuild assets
    checks.append(run_command("make clean", "Clean build artifacts"))
    checks.append(run_command("make assets", "Build assets"))
    
    # Check build outputs
    build_files = [
        "build/weights_matrix.pkl",
        "build/roi_lookup.pkl"
    ]
    
    for file_path in build_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} - BUILT")
            checks.append(True)
        else:
            print(f"❌ {file_path} - MISSING")
            checks.append(False)
    
    return all(checks)

def check_tests() -> bool:
    """Run test suite."""
    print("\n🧪 TEST SUITE CHECKS")
    print("=" * 40)
    
    checks = []
    
    # Run fast tests
    checks.append(run_command("make test-fast", "Parallel test execution"))
    
    # Run quick verification
    checks.append(run_command("python test_week1.py", "Week 1 verification"))
    
    return all(checks)

def check_cli_demo() -> bool:
    """Test CLI demo with edge case persona."""
    print("\n🎭 CLI DEMO CHECKS")
    print("=" * 40)
    
    # Test CLI demo can start (just initialization)
    cmd = 'python -c "from demo_cli import CLIDemo; demo = CLIDemo(); print(\'✅ CLI Demo initializes\' if demo.initialize() else \'❌ CLI Demo failed\')"'
    
    return run_command(cmd, "CLI demo initialization")

def check_bot_startup() -> bool:
    """Test bot can start up (without actually running)."""
    print("\n🤖 BOT STARTUP CHECKS")
    print("=" * 40)
    
    # Test bot imports and initializes
    cmd = '''python -c "
import sys
sys.path.insert(0, 'src')
try:
    from bot.app import EduGuideBotApp
    app = EduGuideBotApp()
    print('✅ Bot app imports successfully')
except Exception as e:
    print(f'❌ Bot app import failed: {e}')
    sys.exit(1)
"'''
    
    return run_command(cmd, "Bot application import")

def main():
    """Run complete sanity check."""
    print("🎓 EDUGUIDEBOT 2025 - SANITY CHECK")
    print("=" * 60)
    print("🔍 Verifying all systems before deployment...")
    print("=" * 60)
    
    start_time = time.time()
    
    # Run all checks
    checks = [
        ("Environment", check_environment),
        ("Dependencies", check_dependencies), 
        ("Asset Building", check_build_assets),
        ("Test Suite", check_tests),
        ("CLI Demo", check_cli_demo),
        ("Bot Startup", check_bot_startup)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} check crashed: {e}")
            results.append((check_name, False))
    
    # Summary
    elapsed = time.time() - start_time
    print("\n" + "=" * 60)
    print("📊 SANITY CHECK RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {check_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} checks passed")
    print(f"⏱️ Completed in {elapsed:.1f} seconds")
    
    if passed == total:
        print("\n🎉 ALL SYSTEMS GO!")
        print("🚀 Ready for presentation/deployment")
        print("\n💡 Quick start commands:")
        print("   • make dev     - Start development bot")
        print("   • make demo    - Run CLI demo")
        print("   • make test    - Run full test suite")
    else:
        print(f"\n⚠️ {total - passed} issues need attention")
        print("🔧 Fix the failing checks before proceeding")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
