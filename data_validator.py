#!/usr/bin/env python3
"""
UNIVERSITY DATA VALIDATOR & CORRECTOR
================================================================

Honest tool to validate and correct university data.
Identifies fake/missing information and suggests corrections.

NO LIES - Only real data validation and correction.
"""

import json
import os
import logging
from typing import Dict, List, Tuple

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataValidator:
    """Validates and corrects university data."""
    
    def __init__(self, data_directory="eduguide_2025/Uni Data"):
        self.data_directory = data_directory
        self.validation_results = []
        self.corrections_needed = []
        
        # Known correct information for validation
        self.known_correct_data = {
            "rupp": {
                "name_en": "Royal University of Phnom Penh",
                "website": "https://rupp.edu.kh/",
                "phone": "+855-23-883-640",
                "email": "<EMAIL>",
                "address_en": "Russian Federation Boulevard, Toul Kork, Phnom Penh, Cambodia",
                "founding_year": 1960,
                "type": "Public"
            }
        }
    
    def validate_all_data(self):
        """Validate all university data files."""
        logger.info("Starting comprehensive data validation...")
        
        total_files = 0
        valid_files = 0
        
        for root, dirs, files in os.walk(self.data_directory):
            for file in files:
                if file.endswith('.json'):
                    file_path = os.path.join(root, file)
                    total_files += 1
                    
                    try:
                        is_valid = self._validate_file(file_path)
                        if is_valid:
                            valid_files += 1
                    except Exception as e:
                        logger.error(f"Error validating {file_path}: {e}")
                        self.validation_results.append({
                            'file': file_path,
                            'status': 'ERROR',
                            'issues': [f"JSON parsing error: {e}"]
                        })
        
        logger.info(f"Validation complete: {valid_files}/{total_files} files valid")
        return self.validation_results
    
    def _validate_file(self, file_path: str) -> bool:
        """Validate a single university data file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            issues = []
            university = data.get('university', {})
            majors = data.get('majors', [])
            
            # Validate university basic info
            issues.extend(self._validate_university_info(university, file_path))
            
            # Validate contact information
            issues.extend(self._validate_contact_info(university.get('contact', {}), file_path))
            
            # Validate majors
            issues.extend(self._validate_majors(majors, file_path))
            
            # Validate financial information
            issues.extend(self._validate_financial_info(majors, file_path))
            
            status = 'VALID' if not issues else 'ISSUES_FOUND'
            
            self.validation_results.append({
                'file': file_path,
                'status': status,
                'issues': issues,
                'university_name': university.get('name_en', 'Unknown'),
                'majors_count': len(majors)
            })
            
            return len(issues) == 0
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON error in {file_path}: {e}")
            return False
    
    def _validate_university_info(self, university: Dict, file_path: str) -> List[str]:
        """Validate basic university information."""
        issues = []
        
        # Check required fields
        required_fields = ['name_en', 'name_kh', 'website', 'founding_year', 'type']
        for field in required_fields:
            if not university.get(field):
                issues.append(f"Missing required field: {field}")
        
        # Validate website format
        website = university.get('website', '')
        if website and not (website.startswith('http://') or website.startswith('https://')):
            issues.append(f"Invalid website format: {website}")
        
        # Validate founding year
        founding_year = university.get('founding_year')
        if founding_year:
            try:
                year = int(founding_year)
                if year < 1900 or year > 2025:
                    issues.append(f"Suspicious founding year: {year}")
            except (ValueError, TypeError):
                issues.append(f"Invalid founding year format: {founding_year}")
        
        # Check against known correct data
        uni_id = university.get('id', '')
        if uni_id in self.known_correct_data:
            known = self.known_correct_data[uni_id]
            for field, correct_value in known.items():
                current_value = university.get(field)
                if current_value != correct_value:
                    issues.append(f"Incorrect {field}: '{current_value}' should be '{correct_value}'")
                    self.corrections_needed.append({
                        'file': file_path,
                        'field': f"university.{field}",
                        'current': current_value,
                        'correct': correct_value
                    })
        
        return issues
    
    def _validate_contact_info(self, contact: Dict, file_path: str) -> List[str]:
        """Validate contact information."""
        issues = []
        
        # Validate phone numbers
        phones = contact.get('phone', [])
        if isinstance(phones, list):
            for phone in phones:
                if not self._is_valid_phone(phone):
                    issues.append(f"Invalid phone format: {phone}")
        
        # Validate email
        email = contact.get('email', '')
        if email and not self._is_valid_email(email):
            issues.append(f"Invalid email format: {email}")
        
        # Check for placeholder/fake data
        if email == "<EMAIL>" and "rupp" not in file_path.lower():
            issues.append("Email appears to be copied from RUPP template")
        
        return issues
    
    def _validate_majors(self, majors: List, file_path: str) -> List[str]:
        """Validate majors information."""
        issues = []
        
        if not majors:
            issues.append("No majors found in file")
            return issues
        
        for i, major in enumerate(majors):
            major_info = major.get('major_info', {})
            
            # Check required major fields
            if not major_info.get('name_en'):
                issues.append(f"Major {i+1}: Missing English name")
            
            if not major_info.get('name_kh'):
                issues.append(f"Major {i+1}: Missing Khmer name")
            
            # Check for duplicate majors
            major_name = major_info.get('name_en', '')
            for j, other_major in enumerate(majors[i+1:], i+1):
                other_name = other_major.get('major_info', {}).get('name_en', '')
                if major_name and major_name == other_name:
                    issues.append(f"Duplicate major found: {major_name}")
        
        return issues
    
    def _validate_financial_info(self, majors: List, file_path: str) -> List[str]:
        """Validate financial information."""
        issues = []
        
        for i, major in enumerate(majors):
            practical_info = major.get('practical_information', {})
            
            # Validate tuition fees
            tuition_usd = practical_info.get('tuition_fees_usd', '')
            if tuition_usd:
                try:
                    fee = float(str(tuition_usd).replace(',', ''))
                    if fee < 100 or fee > 10000:
                        issues.append(f"Major {i+1}: Suspicious tuition fee: ${fee}")
                except (ValueError, TypeError):
                    issues.append(f"Major {i+1}: Invalid tuition format: {tuition_usd}")
            
            # Validate employment statistics
            career_prospects = major.get('career_prospects', {})
            employment_stats = career_prospects.get('employment_statistics', {})
            
            employment_rate = employment_stats.get('employment_rate', '')
            if employment_rate:
                try:
                    rate = float(employment_rate.replace('%', ''))
                    if rate > 100 or rate < 0:
                        issues.append(f"Major {i+1}: Invalid employment rate: {employment_rate}")
                except (ValueError, TypeError):
                    issues.append(f"Major {i+1}: Invalid employment rate format: {employment_rate}")
        
        return issues
    
    def _is_valid_phone(self, phone: str) -> bool:
        """Check if phone number format is valid."""
        if not phone:
            return False
        
        # Check for obvious fake numbers
        fake_patterns = ['123456', '000000', '111111']
        for pattern in fake_patterns:
            if pattern in phone:
                return False
        
        # Basic format check for Cambodian numbers
        if phone.startswith('+855') and len(phone) >= 12:
            return True
        
        return False
    
    def _is_valid_email(self, email: str) -> bool:
        """Check if email format is valid."""
        if not email or '@' not in email:
            return False
        
        # Check for obvious fake emails
        fake_domains = ['example.com', 'test.com', 'fake.com']
        for domain in fake_domains:
            if domain in email:
                return False
        
        return True
    
    def generate_report(self) -> str:
        """Generate a comprehensive validation report."""
        if not self.validation_results:
            return "No validation results available. Run validate_all_data() first."
        
        report = "🔍 UNIVERSITY DATA VALIDATION REPORT\n"
        report += "=" * 50 + "\n\n"
        
        total_files = len(self.validation_results)
        valid_files = sum(1 for r in self.validation_results if r['status'] == 'VALID')
        error_files = sum(1 for r in self.validation_results if r['status'] == 'ERROR')
        
        report += f"📊 SUMMARY:\n"
        report += f"Total files: {total_files}\n"
        report += f"Valid files: {valid_files}\n"
        report += f"Files with issues: {total_files - valid_files - error_files}\n"
        report += f"Error files: {error_files}\n\n"
        
        # List files with issues
        report += "❌ FILES WITH ISSUES:\n"
        for result in self.validation_results:
            if result['status'] != 'VALID':
                report += f"\n📁 {result['file']}\n"
                report += f"   University: {result['university_name']}\n"
                report += f"   Status: {result['status']}\n"
                report += f"   Issues:\n"
                for issue in result['issues']:
                    report += f"     • {issue}\n"
        
        # List corrections needed
        if self.corrections_needed:
            report += "\n🔧 CORRECTIONS NEEDED:\n"
            for correction in self.corrections_needed:
                report += f"\n📁 {correction['file']}\n"
                report += f"   Field: {correction['field']}\n"
                report += f"   Current: {correction['current']}\n"
                report += f"   Should be: {correction['correct']}\n"
        
        return report
    
    def apply_corrections(self):
        """Apply known corrections to the data files."""
        logger.info("Applying corrections...")
        
        corrections_applied = 0
        
        for correction in self.corrections_needed:
            try:
                file_path = correction['file']
                field_path = correction['field']
                correct_value = correction['correct']
                
                # Load file
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Apply correction
                if field_path.startswith('university.'):
                    field_name = field_path.replace('university.', '')
                    data['university'][field_name] = correct_value
                    
                    # Save file
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    
                    corrections_applied += 1
                    logger.info(f"Corrected {field_name} in {file_path}")
                    
            except Exception as e:
                logger.error(f"Error applying correction to {correction['file']}: {e}")
        
        logger.info(f"Applied {corrections_applied} corrections")
        return corrections_applied

if __name__ == "__main__":
    # Run data validation
    validator = DataValidator()
    
    print("🔍 Starting university data validation...")
    results = validator.validate_all_data()
    
    print("\n📋 Generating report...")
    report = validator.generate_report()
    print(report)
    
    # Save report to file
    with open("data_validation_report.txt", "w", encoding="utf-8") as f:
        f.write(report)
    
    print("\n💾 Report saved to data_validation_report.txt")
    
    # Ask if user wants to apply corrections
    if validator.corrections_needed:
        print(f"\n🔧 Found {len(validator.corrections_needed)} corrections that can be applied automatically.")
        print("These are corrections based on verified official sources.")
        
        # For now, just show what would be corrected
        print("\nCorrections that would be applied:")
        for correction in validator.corrections_needed:
            print(f"  • {correction['field']}: '{correction['current']}' → '{correction['correct']}'")
