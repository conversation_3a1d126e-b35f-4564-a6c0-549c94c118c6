#!/usr/bin/env python3
"""
Asset Building Script - Speed Without Slop
================================================================

Optimizes YAML → Python conversion with intelligent caching:
- Only rebuilds when source files change (hash-based)
- Generates optimized Python data structures
- Validates data integrity during build
- Supports production vs development modes
"""

import hashlib
import json
import pickle
import yaml
import csv
import argparse
from pathlib import Path
from typing import Dict, Any, List
import sys

def calculate_file_hash(file_path: Path) -> str:
    """Calculate SHA256 hash of file content."""
    if not file_path.exists():
        return ""
    
    with open(file_path, 'rb') as f:
        return hashlib.sha256(f.read()).hexdigest()

def load_yaml_safe(file_path: Path) -> Dict[str, Any]:
    """Load YAML file safely with error handling."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    except Exception as e:
        print(f"❌ Error loading {file_path}: {e}")
        return {}

def build_weights_matrix(source_dir: Path, output_dir: Path, force: bool = False) -> bool:
    """Build optimized weights matrix from YAML."""
    source_file = source_dir / "weights_matrix.yaml"
    output_file = output_dir / "weights_matrix.pkl"
    hash_file = output_dir / "weights_matrix.hash"
    
    if not source_file.exists():
        print(f"⚠️ Source file not found: {source_file}")
        return False
    
    # Check if rebuild needed
    current_hash = calculate_file_hash(source_file)
    
    if not force and hash_file.exists() and output_file.exists():
        with open(hash_file, 'r') as f:
            stored_hash = f.read().strip()
        
        if current_hash == stored_hash:
            print(f"✅ Weights matrix up to date (hash: {current_hash[:8]}...)")
            return True
    
    print(f"🔧 Building weights matrix from {source_file}...")
    
    # Load and validate YAML
    weights_data = load_yaml_safe(source_file)
    if not weights_data:
        print("❌ Failed to load weights matrix")
        return False
    
    # Validate structure
    required_categories = ['location', 'budget', 'learning_mode', 'interest_field']
    missing_categories = [cat for cat in required_categories if cat not in weights_data]
    
    if missing_categories:
        print(f"⚠️ Missing required categories: {missing_categories}")
    
    # Optimize data structure for fast lookup
    optimized_weights = {}
    total_rules = 0
    
    for category, answers in weights_data.items():
        optimized_weights[category] = {}
        for answer, rules in answers.items():
            if isinstance(rules, dict):
                optimized_weights[category][answer] = rules
                total_rules += len(rules)
            else:
                print(f"⚠️ Invalid rules format in {category}.{answer}")
    
    # Save optimized version
    output_dir.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'wb') as f:
        pickle.dump(optimized_weights, f, protocol=pickle.HIGHEST_PROTOCOL)
    
    with open(hash_file, 'w') as f:
        f.write(current_hash)
    
    print(f"✅ Built weights matrix: {len(optimized_weights)} categories, {total_rules} rules")
    return True

def build_personas(source_dir: Path, output_dir: Path, force: bool = False) -> bool:
    """Build optimized test personas from YAML."""
    source_file = source_dir / "edge_personas.yaml"
    output_file = output_dir / "edge_personas.json"
    hash_file = output_dir / "edge_personas.hash"
    
    if not source_file.exists():
        print(f"⚠️ Source file not found: {source_file}")
        return False
    
    # Check if rebuild needed
    current_hash = calculate_file_hash(source_file)
    
    if not force and hash_file.exists() and output_file.exists():
        with open(hash_file, 'r') as f:
            stored_hash = f.read().strip()
        
        if current_hash == stored_hash:
            print(f"✅ Test personas up to date (hash: {current_hash[:8]}...)")
            return True
    
    print(f"🔧 Building test personas from {source_file}...")
    
    # Load and validate YAML
    personas_data = load_yaml_safe(source_file)
    if not personas_data:
        print("❌ Failed to load test personas")
        return False
    
    personas = personas_data.get('test_personas', [])
    if not personas:
        print("⚠️ No test personas found")
        return False
    
    # Validate persona structure
    valid_personas = []
    for i, persona in enumerate(personas):
        if not isinstance(persona, dict):
            print(f"⚠️ Invalid persona format at index {i}")
            continue
        
        required_fields = ['id', 'answers']
        missing_fields = [field for field in required_fields if field not in persona]
        
        if missing_fields:
            print(f"⚠️ Persona {persona.get('id', i)} missing fields: {missing_fields}")
            continue
        
        valid_personas.append(persona)
    
    # Save optimized version
    output_dir.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(valid_personas, f, ensure_ascii=False, indent=2)
    
    with open(hash_file, 'w') as f:
        f.write(current_hash)
    
    print(f"✅ Built test personas: {len(valid_personas)} valid personas")
    return True

def build_program_index(source_dir: Path, output_dir: Path, force: bool = False) -> bool:
    """Build memory-optimized program index for fast recommendations."""
    # This would scan university JSON files and create:
    # 1. Compact weight matrix (int8 arrays)
    # 2. Program ID list with minimal metadata
    # 3. Separate detailed lookup for on-demand loading

    print("🔧 Program index optimization - placeholder for future implementation")
    print("💡 Current implementation loads full program objects")
    print("🎯 Future: Vectorized scoring with numpy int8 matrices")
    return True

def build_roi_data(source_dir: Path, output_dir: Path, force: bool = False) -> bool:
    """Build ROI reference data from CSV."""
    source_file = source_dir / "roi_reference.csv"
    output_file = output_dir / "roi_lookup.pkl"
    hash_file = output_dir / "roi_reference.hash"

    if not source_file.exists():
        print(f"⚠️ ROI reference file not found: {source_file}")
        return False

    # Check if rebuild needed
    current_hash = calculate_file_hash(source_file)

    if not force and hash_file.exists() and output_file.exists():
        with open(hash_file, 'r') as f:
            stored_hash = f.read().strip()

        if current_hash == stored_hash:
            print(f"✅ ROI data up to date (hash: {current_hash[:8]}...)")
            return True

    print(f"🔧 Building ROI lookup from {source_file}...")

    # Load and process CSV
    roi_lookup = {}

    try:
        with open(source_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                field_tag = row['field_tag']
                roi_lookup[field_tag] = {
                    'avg_salary_usd': float(row['avg_salary_usd']),
                    'employment_rate': float(row['employment_rate']),
                    'source': row['source'],
                    'year': row['year']
                }

        # Save optimized lookup
        output_dir.mkdir(parents=True, exist_ok=True)

        with open(output_file, 'wb') as f:
            pickle.dump(roi_lookup, f, protocol=pickle.HIGHEST_PROTOCOL)

        with open(hash_file, 'w') as f:
            f.write(current_hash)

        print(f"✅ Built ROI lookup: {len(roi_lookup)} field mappings")
        return True

    except Exception as e:
        print(f"❌ Error building ROI data: {e}")
        return False

def build_i18n(source_dir: Path, output_dir: Path, force: bool = False) -> bool:
    """Build internationalization files."""
    # For now, just copy Khmer strings from assessment questions
    # In future, this could build from centralized i18n YAML files

    print("✅ I18n build skipped (embedded in code for now)")
    return True

def validate_build_outputs(output_dir: Path) -> bool:
    """Validate that all build outputs are correct."""
    print("🔍 Validating build outputs...")
    
    # Check weights matrix
    weights_file = output_dir / "weights_matrix.pkl"
    if weights_file.exists():
        try:
            with open(weights_file, 'rb') as f:
                weights = pickle.load(f)
            print(f"✅ Weights matrix valid: {len(weights)} categories")
        except Exception as e:
            print(f"❌ Weights matrix corrupted: {e}")
            return False
    
    # Check personas
    personas_file = output_dir / "edge_personas.json"
    if personas_file.exists():
        try:
            with open(personas_file, 'r', encoding='utf-8') as f:
                personas = json.load(f)
            print(f"✅ Test personas valid: {len(personas)} personas")
        except Exception as e:
            print(f"❌ Test personas corrupted: {e}")
            return False
    
    return True

def main():
    """Main build script."""
    parser = argparse.ArgumentParser(description="Build EduGuideBot assets")
    parser.add_argument("--force", action="store_true", help="Force rebuild even if up to date")
    parser.add_argument("--production", action="store_true", help="Production build mode")
    parser.add_argument("--validate-only", action="store_true", help="Only validate existing builds")
    
    args = parser.parse_args()
    
    # Paths
    project_root = Path(__file__).parent.parent
    source_dir = project_root / "data"
    output_dir = project_root / "build"
    
    print("🏗️ EduGuideBot Asset Builder")
    print("=" * 40)
    
    if args.validate_only:
        success = validate_build_outputs(output_dir)
        sys.exit(0 if success else 1)
    
    # Build all assets
    builders = [
        ("Weights Matrix", lambda: build_weights_matrix(source_dir, output_dir, args.force)),
        ("Test Personas", lambda: build_personas(source_dir, output_dir, args.force)),
        ("ROI Data", lambda: build_roi_data(source_dir, output_dir, args.force)),
        ("Program Index", lambda: build_program_index(source_dir, output_dir, args.force)),
        ("I18n Files", lambda: build_i18n(source_dir, output_dir, args.force))
    ]
    
    success_count = 0
    
    for name, builder in builders:
        try:
            if builder():
                success_count += 1
            else:
                print(f"❌ Failed to build {name}")
        except Exception as e:
            print(f"❌ Error building {name}: {e}")
    
    # Validate outputs
    if success_count > 0:
        validate_build_outputs(output_dir)
    
    print("=" * 40)
    print(f"🎯 Build complete: {success_count}/{len(builders)} successful")
    
    if args.production:
        print("🚀 Production build ready for deployment")
    
    sys.exit(0 if success_count == len(builders) else 1)

if __name__ == "__main__":
    main()
