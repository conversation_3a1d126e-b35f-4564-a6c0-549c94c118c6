#!/usr/bin/env python3
"""
Asset Builder - Convert Raw Data to Clean Format
================================================================

Converts existing JSON files to clean JSONL format and builds feature matrices.
"""

import json
import logging
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

def convert_json_to_jsonl():
    """Convert existing JSON files to clean JSONL format."""
    source_dir = Path("eduguide_2025/Uni Data")
    output_file = Path("data/universities.jsonl")
    
    if not source_dir.exists():
        logger.error(f"❌ Source directory not found: {source_dir}")
        return False
    
    universities = []
    
    # Process each city directory
    for city_dir in source_dir.iterdir():
        if not city_dir.is_dir():
            continue
        
        city_code = city_dir.name  # PP, SR, BTB
        
        # Process each university JSON file
        for json_file in city_dir.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Extract university info
                uni_info = data.get('university_info', {})
                majors = data.get('majors', [])
                
                # Create clean university record
                university = {
                    "id": json_file.stem,
                    "name_kh": uni_info.get('name_kh', ''),
                    "name_en": uni_info.get('name_en', ''),
                    "city": city_code,
                    "type": uni_info.get('type', 'private'),
                    "majors": []
                }
                
                # Process majors
                for major in majors:
                    major_info = major.get('major_info', {})
                    practical_info = major.get('practical_information', {})
                    career_prospects = major.get('career_prospects', {})
                    
                    # Extract employment statistics
                    employment_stats = career_prospects.get('employment_statistics', {})
                    employment_rate = extract_percentage(employment_stats.get('employment_rate', '0%'))
                    starting_salary = extract_salary(employment_stats.get('average_starting_salary', '0'))
                    
                    clean_major = {
                        "major_id": major_info.get('id', ''),
                        "name_kh": major_info.get('name_kh', ''),
                        "name_en": major_info.get('name_en', ''),
                        "tuition_usd": extract_tuition(practical_info),
                        "field_tag": infer_field_tag(major_info.get('name_en', '')),
                        "career_cluster": infer_career_cluster(major_info.get('name_en', '')),
                        "employment_rate": employment_rate,
                        "starting_salary_usd": starting_salary
                    }
                    
                    university["majors"].append(clean_major)
                
                universities.append(university)
                logger.info(f"✅ Processed {university['name_en']} ({len(university['majors'])} majors)")
                
            except Exception as e:
                logger.error(f"❌ Error processing {json_file}: {e}")
    
    # Write to JSONL file
    output_file.parent.mkdir(parents=True, exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as f:
        for university in universities:
            f.write(json.dumps(university, ensure_ascii=False) + '\n')
    
    logger.info(f"✅ Converted {len(universities)} universities to {output_file}")
    return True

def build_feature_matrix():
    """Build feature matrix for ML training."""
    input_file = Path("data/universities.jsonl")
    output_file = Path("build/programs.parquet")
    
    if not input_file.exists():
        logger.error(f"❌ Input file not found: {input_file}")
        return False
    
    programs = []
    
    # Read JSONL file
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in f:
            university = json.loads(line)
            
            for major in university['majors']:
                program = {
                    'program_id': f"{university['id']}_{major['major_id']}",
                    'university_id': university['id'],
                    'university_name_kh': university['name_kh'],
                    'university_name_en': university['name_en'],
                    'major_name_kh': major['name_kh'],
                    'major_name_en': major['name_en'],
                    'city': university['city'],
                    'tuition_usd': major['tuition_usd'],
                    'field_tag': major['field_tag'],
                    'career_cluster': major['career_cluster'],
                    'employment_rate': major['employment_rate'],
                    'starting_salary_usd': major['starting_salary_usd']
                }
                programs.append(program)
    
    # Create DataFrame and save as Parquet
    df = pd.DataFrame(programs)
    output_file.parent.mkdir(parents=True, exist_ok=True)
    df.to_parquet(output_file, index=False)
    
    logger.info(f"✅ Built feature matrix with {len(programs)} programs: {output_file}")
    return True

def extract_percentage(value: str) -> float:
    """Extract percentage from string."""
    try:
        if isinstance(value, str) and '%' in value:
            return float(value.replace('%', '')) / 100
        return float(value) if value else 0.0
    except:
        return 0.0

def extract_salary(value: str) -> float:
    """Extract salary from string."""
    try:
        if isinstance(value, str):
            clean_value = value.replace('$', '').replace(',', '').replace('USD', '').strip()
            return float(clean_value) if clean_value else 0.0
        return float(value) if value else 0.0
    except:
        return 0.0

def extract_tuition(practical_info: Dict) -> float:
    """Extract tuition from practical information."""
    try:
        for field in ['tuition_fees_usd', 'tuition_usd', 'fees_usd']:
            if field in practical_info:
                return extract_salary(str(practical_info[field]))
        
        if 'fees' in practical_info:
            fees = practical_info['fees']
            if isinstance(fees, dict):
                for field in ['tuition_usd', 'annual_usd', 'total_usd']:
                    if field in fees:
                        return extract_salary(str(fees[field]))
        
        return 0.0
    except:
        return 0.0

def infer_field_tag(major_name: str) -> str:
    """Infer field tag from major name."""
    major_lower = major_name.lower()
    
    if any(term in major_lower for term in ['engineering', 'computer', 'technology', 'science', 'mathematics']):
        return "STEM"
    elif any(term in major_lower for term in ['business', 'management', 'economics', 'finance']):
        return "BUS"
    elif any(term in major_lower for term in ['arts', 'design', 'creative', 'media']):
        return "ARTS"
    elif any(term in major_lower for term in ['health', 'medical', 'nursing', 'pharmacy']):
        return "HLTH"
    else:
        return "OTHER"

def infer_career_cluster(major_name: str) -> str:
    """Infer career cluster from major name."""
    major_lower = major_name.lower()
    
    if any(term in major_lower for term in ['computer', 'software', 'it', 'technology']):
        return "TECH"
    elif any(term in major_lower for term in ['engineering', 'technical']):
        return "ENGR"
    elif any(term in major_lower for term in ['business', 'management']):
        return "MGT"
    elif any(term in major_lower for term in ['finance', 'accounting']):
        return "FIN"
    else:
        return "OTHER"

def build_all():
    """Build all assets."""
    logger.info("🔧 Building assets...")
    
    success = True
    
    # Convert JSON to JSONL
    if not convert_json_to_jsonl():
        success = False
    
    # Build feature matrix
    if not build_feature_matrix():
        success = False
    
    if success:
        logger.info("✅ Asset building complete")
    else:
        logger.error("❌ Asset building failed")
    
    return success

def main():
    """Main function."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    logger.info("🏗️ ASSET BUILDER")
    logger.info("=" * 40)
    
    success = build_all()
    
    if success:
        logger.info("🎉 All assets built successfully!")
    else:
        logger.error("💥 Asset building failed!")
        exit(1)

if __name__ == "__main__":
    main()
