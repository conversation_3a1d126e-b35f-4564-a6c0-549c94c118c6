#!/usr/bin/env python3
"""
Persona Generator - Speed Without Slop
================================================================

Generates test personas for edge case testing:
- Creates YAML stubs with realistic Khmer answer combinations
- Supports both edge cases and random scenarios
- Maintains consistent format for easy manual editing
- Validates generated personas against question schema
"""

import random
import yaml
import argparse
from pathlib import Path
from typing import Dict, List, Any

# Question schema with all possible answers
QUESTION_SCHEMA = {
    'location': ['phnom_penh', 'siem_reap', 'battambang', 'online'],
    'budget': ['low', 'mid', 'high'],
    'learning_mode': ['traditional', 'blended', 'online_only'],
    'interest_field': ['stem', 'arts', 'business', 'agro_env', 'health'],
    'career_goal': ['digital_tech', 'engineering', 'finance', 'education', 'unsure'],
    'language_level': ['none', 'medium', 'high'],
    'strength_profile': ['logic_math', 'balanced', 'creative'],
    'need_internship': ['yes', 'maybe', 'no'],
    'scholarship': ['need', 'maybe', 'none'],
    'doc_support': ['self_prepare', 'need_help'],
    'study_time': ['daytime', 'evening', 'flexible'],
    'internet_access': ['stable', 'mobile_only', 'weak'],
    'green_value': ['high', 'medium', 'low'],
    'work_abroad': ['local', 'asean', 'global'],
    'academic_performance': ['grade_a', 'grade_b', 'grade_c', 'grade_d'],
    'work_style': ['teamwork', 'individual', 'both']
}

# Edge case templates with specific constraints
EDGE_CASE_TEMPLATES = [
    {
        'id': 'extreme_low_budget_tech',
        'description': 'Low budget student wanting expensive tech career',
        'constraints': {
            'budget': 'low',
            'interest_field': 'stem',
            'career_goal': 'digital_tech',
            'scholarship': 'need'
        },
        'expected_behavior': 'Should filter out expensive programs'
    },
    {
        'id': 'rural_agriculture_evening',
        'description': 'Rural student needing evening classes',
        'constraints': {
            'location': 'battambang',
            'interest_field': 'agro_env',
            'study_time': 'evening',
            'budget': 'low'
        },
        'expected_behavior': 'Should prioritize local agriculture programs'
    },
    {
        'id': 'premium_business_traditional',
        'description': 'High budget business student preferring campus',
        'constraints': {
            'location': 'phnom_penh',
            'budget': 'high',
            'interest_field': 'business',
            'learning_mode': 'traditional'
        },
        'expected_behavior': 'Should recommend premium PP business schools'
    },
    {
        'id': 'online_only_weak_internet',
        'description': 'Student wanting online but with poor connection',
        'constraints': {
            'learning_mode': 'online_only',
            'internet_access': 'weak',
            'location': 'online'
        },
        'expected_behavior': 'Should handle conflicting requirements gracefully'
    },
    {
        'id': 'creative_arts_green_campus',
        'description': 'Creative student valuing environmental campus',
        'constraints': {
            'interest_field': 'arts',
            'strength_profile': 'creative',
            'green_value': 'high',
            'work_abroad': 'local'
        },
        'expected_behavior': 'Should match personality and values'
    }
]

def generate_random_answers() -> Dict[str, str]:
    """Generate random but realistic answer combination."""
    answers = {}
    
    for question, options in QUESTION_SCHEMA.items():
        # Add some realistic weighting
        if question == 'location':
            # PP is most common
            weights = [0.5, 0.2, 0.2, 0.1]
        elif question == 'budget':
            # Mid budget most common
            weights = [0.3, 0.5, 0.2]
        elif question == 'learning_mode':
            # Traditional still preferred
            weights = [0.5, 0.3, 0.2]
        else:
            # Equal probability for others
            weights = [1.0 / len(options)] * len(options)
        
        answers[question] = random.choices(options, weights=weights)[0]
    
    return answers

def generate_edge_case_persona(template: Dict[str, Any]) -> Dict[str, Any]:
    """Generate persona from edge case template."""
    # Start with random answers
    answers = generate_random_answers()
    
    # Apply constraints from template
    constraints = template.get('constraints', {})
    for question, value in constraints.items():
        answers[question] = value
    
    # Create persona
    persona = {
        'id': template['id'],
        'description': template['description'],
        'answers': answers,
        'expected_behavior': template.get('expected_behavior', ''),
        'min_recommendations': 2
    }
    
    # Add specific expectations based on constraints
    if 'budget' in constraints and constraints['budget'] == 'low':
        persona['must_not_recommend'] = ['tuition_bracket=high']
    
    if 'interest_field' in constraints:
        field_map = {
            'stem': 'STEM',
            'business': 'BUS',
            'arts': 'ARTS',
            'agro_env': 'AGRI',
            'health': 'HLTH'
        }
        persona['expected_top_field'] = field_map.get(constraints['interest_field'])
    
    if 'location' in constraints and constraints['location'] != 'online':
        city_map = {
            'phnom_penh': 'PP',
            'siem_reap': 'SR',
            'battambang': 'BB'
        }
        persona['expected_uni_city'] = city_map.get(constraints['location'])
    
    return persona

def generate_random_persona(persona_id: str) -> Dict[str, Any]:
    """Generate completely random persona for stress testing."""
    answers = generate_random_answers()
    
    persona = {
        'id': f'random_{persona_id}',
        'description': f'Random test persona {persona_id}',
        'answers': answers,
        'min_recommendations': 1
    }
    
    return persona

def validate_persona(persona: Dict[str, Any]) -> List[str]:
    """Validate persona structure and content."""
    errors = []
    
    # Check required fields
    required_fields = ['id', 'answers']
    for field in required_fields:
        if field not in persona:
            errors.append(f"Missing required field: {field}")
    
    # Validate answers
    if 'answers' in persona:
        answers = persona['answers']
        for question, answer in answers.items():
            if question not in QUESTION_SCHEMA:
                errors.append(f"Unknown question: {question}")
            elif answer not in QUESTION_SCHEMA[question]:
                errors.append(f"Invalid answer for {question}: {answer}")
    
    return errors

def main():
    """Main persona generator."""
    parser = argparse.ArgumentParser(description="Generate test personas")
    parser.add_argument("--edge", type=int, default=5, help="Number of edge case personas")
    parser.add_argument("--random", type=int, default=10, help="Number of random personas")
    parser.add_argument("--output", type=str, default="data/generated_personas.yaml", 
                       help="Output file path")
    parser.add_argument("--validate", action="store_true", help="Validate existing personas")
    
    args = parser.parse_args()
    
    if args.validate:
        # Validate existing personas
        personas_file = Path("data/edge_personas.yaml")
        if not personas_file.exists():
            print("❌ No personas file found to validate")
            return
        
        with open(personas_file, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        personas = data.get('test_personas', [])
        total_errors = 0
        
        for persona in personas:
            errors = validate_persona(persona)
            if errors:
                print(f"❌ Persona {persona.get('id', 'unknown')} has errors:")
                for error in errors:
                    print(f"   • {error}")
                total_errors += len(errors)
        
        if total_errors == 0:
            print(f"✅ All {len(personas)} personas are valid")
        else:
            print(f"❌ Found {total_errors} validation errors")
        
        return
    
    print("🤖 Generating test personas...")
    print(f"📊 Edge cases: {args.edge}, Random: {args.random}")
    
    personas = []
    
    # Generate edge case personas
    edge_templates = EDGE_CASE_TEMPLATES[:args.edge]
    for template in edge_templates:
        persona = generate_edge_case_persona(template)
        personas.append(persona)
        print(f"✅ Generated edge case: {persona['id']}")
    
    # Generate random personas
    for i in range(args.random):
        persona = generate_random_persona(f"{i+1:03d}")
        personas.append(persona)
        if i < 3:  # Only show first few
            print(f"✅ Generated random: {persona['id']}")
    
    if args.random > 3:
        print(f"✅ Generated {args.random - 3} more random personas...")
    
    # Validate all personas
    total_errors = 0
    for persona in personas:
        errors = validate_persona(persona)
        total_errors += len(errors)
    
    if total_errors > 0:
        print(f"⚠️ Generated personas have {total_errors} validation errors")
    
    # Save to file
    output_data = {
        'test_personas': personas,
        'metadata': {
            'generated_count': len(personas),
            'edge_cases': args.edge,
            'random_cases': args.random,
            'validation_errors': total_errors
        }
    }
    
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        yaml.dump(output_data, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    print(f"💾 Saved {len(personas)} personas to {output_path}")
    print("🎯 Ready for testing!")

if __name__ == "__main__":
    main()
