#!/usr/bin/env python3
"""
Week 1 Deliverables Test Runner
================================================================

Quick test to verify all Week 1 components are working:
- MCDA engine initialization
- Weight matrix loading
- Edge persona testing
- Basic recommendation generation
"""

import sys
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_mcda_engine():
    """Test MCDA engine basic functionality."""
    print("🧪 Testing MCDA Engine...")
    
    try:
        from mcda_recommender import MCDARecommendationEngine
        
        # Initialize engine
        engine = MCDARecommendationEngine()
        
        if not engine.is_ready:
            print("❌ MCDA engine not ready")
            return False
        
        print(f"✅ MCDA engine loaded: {len(engine.programs)} programs")
        print(f"✅ Weight matrix loaded: {len(engine.weights_matrix)} categories")
        
        return True
        
    except Exception as e:
        print(f"❌ MCDA engine test failed: {e}")
        return False

def test_basic_recommendations():
    """Test basic recommendation generation."""
    print("\n🧪 Testing Basic Recommendations...")
    
    try:
        from mcda_recommender import MCDARecommendationEngine
        
        engine = MCDARecommendationEngine()
        
        # Simple test case
        test_answers = {
            'location': 'phnom_penh',
            'budget': 'mid',
            'learning_mode': 'traditional',
            'interest_field': 'stem',
            'career_goal': 'digital_tech'
        }
        
        recommendations = engine.get_recommendations(test_answers, limit=3)
        
        if not recommendations:
            print("❌ No recommendations generated")
            return False
        
        print(f"✅ Generated {len(recommendations)} recommendations")
        
        # Test first recommendation
        rec = recommendations[0]
        print(f"✅ Top recommendation: {rec.program.university_name}")
        print(f"✅ Major: {rec.program.major_name}")
        print(f"✅ Match: {rec.match_percentage:.1f}%")
        print(f"✅ Explanation: {rec.explanation_kh[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic recommendations test failed: {e}")
        return False

def test_edge_personas():
    """Test edge case personas."""
    print("\n🧪 Testing Edge Case Personas...")
    
    try:
        import yaml
        from mcda_recommender import MCDARecommendationEngine
        
        # Load test personas
        personas_file = Path("data/edge_personas.yaml")
        if not personas_file.exists():
            print("❌ Edge personas file not found")
            return False
        
        with open(personas_file, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        personas = data.get('test_personas', [])
        if not personas:
            print("❌ No test personas found")
            return False
        
        print(f"✅ Loaded {len(personas)} test personas")
        
        engine = MCDARecommendationEngine()
        
        # Test first persona
        persona = personas[0]
        answers = persona.get('answers', {})
        
        recommendations = engine.get_recommendations(answers, limit=5)
        
        if not recommendations:
            print(f"❌ No recommendations for persona: {persona.get('id')}")
            return False
        
        print(f"✅ Persona '{persona.get('id')}' got {len(recommendations)} recommendations")
        
        # Check exclusion rules
        if 'must_not_recommend' in persona:
            exclusions = persona['must_not_recommend']
            for rec in recommendations:
                for exclusion in exclusions:
                    if engine._program_matches_condition(rec.program, exclusion):
                        print(f"⚠️ Warning: Found excluded condition {exclusion}")
        
        return True
        
    except Exception as e:
        print(f"❌ Edge personas test failed: {e}")
        return False

def test_weight_matrix():
    """Test weight matrix loading and structure."""
    print("\n🧪 Testing Weight Matrix...")
    
    try:
        import yaml
        
        weights_file = Path("data/weights_matrix.yaml")
        if not weights_file.exists():
            print("❌ Weight matrix file not found")
            return False
        
        with open(weights_file, 'r', encoding='utf-8') as f:
            weights = yaml.safe_load(f)
        
        if not weights:
            print("❌ Weight matrix is empty")
            return False
        
        print(f"✅ Weight matrix loaded: {len(weights)} question categories")
        
        # Check key categories
        expected_categories = ['location', 'budget', 'learning_mode', 'interest_field', 'career_goal']
        for category in expected_categories:
            if category in weights:
                print(f"✅ Found category: {category}")
            else:
                print(f"⚠️ Missing category: {category}")
        
        return True
        
    except Exception as e:
        print(f"❌ Weight matrix test failed: {e}")
        return False

def main():
    """Run all Week 1 tests."""
    print("🎯 WEEK 1 DELIVERABLES TEST SUITE")
    print("=" * 50)
    
    tests = [
        ("Weight Matrix", test_weight_matrix),
        ("MCDA Engine", test_mcda_engine),
        ("Basic Recommendations", test_basic_recommendations),
        ("Edge Personas", test_edge_personas)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL WEEK 1 DELIVERABLES WORKING!")
        print("🚀 Ready for Week 2 development")
    else:
        print("⚠️ Some issues need attention before proceeding")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
