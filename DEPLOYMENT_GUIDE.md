# 🎓 EduGuideBot 2025 - Deployment Guide

## 🚀 Quick Start

### 1. Install Dependencies
```bash
# Essential dependency for Telegram bot
pip install python-telegram-bot

# Or use the simple requirements file
pip install -r requirements_simple.txt
```

### 2. Run System Test
```bash
# Test the enhanced recommendation system
python3 run_bot.py --test
```

### 3. Start the Bot
```bash
# Start the Telegram bot
python3 run_bot.py
```

## 📋 File Structure

### Core Files (Required)
- `main_bot.py` - Main Telegram bot implementation
- `eduguide_enhanced.py` - Enhanced recommendation engine
- `run_bot.py` - Launcher script with dependency checking
- `eduguide_2025/Uni Data/` - University database (JSON files)

### Optional Files
- `requirements_simple.txt` - Minimal dependencies
- `DEPLOYMENT_GUIDE.md` - This guide
- `synthetic_data_generator.py` - For generating training data
- `advanced_ml_system.py` - Advanced ML experiments

## 🔧 Commands

### Check Dependencies
```bash
python3 run_bot.py --check
```

### Run System Test
```bash
python3 run_bot.py --test
```

### Run Interactive Demo
```bash
python3 run_bot.py --demo
```

### Start Telegram Bot
```bash
python3 run_bot.py
# or simply
python3 main_bot.py
```

### Show Help
```bash
python3 run_bot.py --help
```

## 🤖 Bot Information

- **Bot Token:** `8198268528:AAHuAvp3bRqO5hdPD3tbv1xSks2YjXtiu9Y`
- **Language:** 100% Khmer (Cambodian)
- **Target Users:** Cambodian students
- **Assessment:** 16 questions, 7-dimensional analysis

## 🎯 Features

### ✅ Working Features
- **Enhanced Recommendation System** with 7-dimensional scoring
- **514+ University Majors** from 64+ universities
- **Advanced Keyword Matching** with semantic similarity
- **Comprehensive Explanations** in Khmer language
- **ROI Analysis** (Return on Investment calculations)
- **Diversity Filtering** for varied recommendations
- **Real University Data** with accurate tuition and employment stats

### 🔧 Technical Features
- **Advanced Mathematical Algorithms** (not fake ML)
- **Multi-dimensional Scoring System**
- **Enhanced Pattern Recognition**
- **Sophisticated Recommendation Engine**
- **Cultural Context Awareness**

## 📊 System Requirements

### Minimum Requirements
- Python 3.8+
- 50MB disk space
- Internet connection for Telegram API

### Recommended
- Python 3.9+
- 100MB disk space
- Stable internet connection

## 🚫 Honest Limitations

### What We DON'T Have
- ❌ Neural networks (execution environment limitations)
- ❌ Deep learning models (heavy dependencies don't work)
- ❌ Real machine learning training (pandas/sklearn issues)

### What We DO Have
- ✅ Advanced mathematical scoring algorithms
- ✅ Sophisticated pattern recognition
- ✅ Comprehensive data analysis
- ✅ Real working functionality

## 🔍 Troubleshooting

### Bot Won't Start
1. Check dependencies: `python3 run_bot.py --check`
2. Install Telegram library: `pip install python-telegram-bot`
3. Verify files are present: `ls -la *.py`

### System Test Fails
1. Check if `eduguide_enhanced.py` exists
2. Verify university data directory: `ls -la eduguide_2025/Uni\ Data/`
3. Run with verbose output: `python3 eduguide_enhanced.py`

### Import Errors
1. Make sure all files are in the same directory
2. Check Python version: `python3 --version`
3. Reinstall dependencies: `pip install --upgrade python-telegram-bot`

## 📱 Using the Bot

### 1. Start Conversation
- Send `/start` to the bot
- Bot will show welcome message in Khmer

### 2. Take Assessment
- Click "ចាប់ផ្តើមការវាយតម្លៃ" (Start Assessment)
- Answer 16 questions about your preferences
- Questions cover academics, personality, career goals, budget

### 3. Get Recommendations
- Bot analyzes your answers using 7-dimensional algorithm
- Receive 5 personalized university/major recommendations
- Each recommendation includes detailed explanation in Khmer

### 4. View Details
- Click on any recommendation for detailed analysis
- See score breakdown and reasoning
- Understand why each major was recommended

## 🎓 For University Presentation

### Key Points to Emphasize
1. **100% Khmer Language** - Fully localized for Cambodian students
2. **Real Data** - 514+ majors, accurate tuition and employment statistics
3. **Advanced Algorithms** - 7-dimensional scoring system
4. **Comprehensive Analysis** - Multi-factor recommendation engine
5. **Cultural Appropriateness** - Designed specifically for Cambodian context

### Technical Achievements
- **Advanced Mathematical Scoring** (honest about not being neural networks)
- **Sophisticated Pattern Recognition** using keyword networks
- **Multi-dimensional Analysis** with weighted scoring
- **Enhanced User Experience** with detailed explanations
- **Robust System Architecture** with error handling

### Honest Positioning
- "Advanced Mathematical Recommendation System"
- "Sophisticated Algorithmic Analysis"
- "Multi-dimensional Scoring Engine"
- NOT "Deep Learning" or "Neural Networks" (be honest)

## 📞 Support

If you encounter issues:
1. Check this deployment guide
2. Run system diagnostics: `python3 run_bot.py --check`
3. Review error messages carefully
4. Ensure all files are present and accessible

## ✅ Success Indicators

The system is working correctly when:
- ✅ System test passes: `python3 run_bot.py --test`
- ✅ Bot responds to `/start` command
- ✅ Assessment questions appear in Khmer
- ✅ Recommendations are generated with scores
- ✅ Detailed explanations are provided in Khmer

---

**🎉 EduGuideBot 2025 - Advanced Mathematical Recommendation System for Cambodian Students**
