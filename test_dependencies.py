#!/usr/bin/env python3
"""
DEPENDENCY TESTING - GRADUAL ADDITION APPROACH
Testing each dependency individually to identify what works
"""

import sys

def test_dependency(name, import_statement):
    """Test individual dependency."""
    try:
        exec(import_statement)
        print(f"✅ {name}: WORKING")
        return True
    except ImportError as e:
        print(f"❌ {name}: NOT INSTALLED - {e}")
        return False
    except Exception as e:
        print(f"⚠️ {name}: ERROR - {e}")
        return False

def main():
    print("🔧 TESTING DEPENDENCIES SYSTEMATICALLY...")
    print("=" * 50)
    
    # Test basic dependencies first
    basic_deps = [
        ("JSON", "import json"),
        ("OS", "import os"),
        ("Logging", "import logging"),
        ("DateTime", "from datetime import datetime"),
    ]
    
    print("📋 BASIC DEPENDENCIES:")
    basic_working = 0
    for name, import_stmt in basic_deps:
        if test_dependency(name, import_stmt):
            basic_working += 1
    
    print(f"\n✅ Basic dependencies: {basic_working}/{len(basic_deps)} working")
    
    # Test data science dependencies
    print("\n📊 DATA SCIENCE DEPENDENCIES:")
    data_deps = [
        ("NumPy", "import numpy as np"),
        ("Pandas", "import pandas as pd"),
    ]
    
    data_working = 0
    for name, import_stmt in data_deps:
        if test_dependency(name, import_stmt):
            data_working += 1
    
    print(f"\n✅ Data science dependencies: {data_working}/{len(data_deps)} working")
    
    # Test ML dependencies
    print("\n🤖 MACHINE LEARNING DEPENDENCIES:")
    ml_deps = [
        ("Scikit-learn", "from sklearn.ensemble import RandomForestRegressor"),
        ("Scikit-learn Preprocessing", "from sklearn.preprocessing import StandardScaler"),
        ("Scikit-learn TF-IDF", "from sklearn.feature_extraction.text import TfidfVectorizer"),
    ]
    
    ml_working = 0
    for name, import_stmt in ml_deps:
        if test_dependency(name, import_stmt):
            ml_working += 1
    
    print(f"\n✅ ML dependencies: {ml_working}/{len(ml_deps)} working")
    
    # Test Telegram dependencies
    print("\n📱 TELEGRAM DEPENDENCIES:")
    telegram_deps = [
        ("Python Telegram Bot", "from telegram import Update"),
        ("Telegram Extensions", "from telegram.ext import Application"),
    ]
    
    telegram_working = 0
    for name, import_stmt in telegram_deps:
        if test_dependency(name, import_stmt):
            telegram_working += 1
    
    print(f"\n✅ Telegram dependencies: {telegram_working}/{len(telegram_deps)} working")
    
    # Test heavy AI dependencies
    print("\n🧠 HEAVY AI DEPENDENCIES:")
    ai_deps = [
        ("PyTorch", "import torch"),
        ("Transformers", "from transformers import pipeline"),
        ("Sentence Transformers", "from sentence_transformers import SentenceTransformer"),
    ]
    
    ai_working = 0
    for name, import_stmt in ai_deps:
        if test_dependency(name, import_stmt):
            ai_working += 1
    
    print(f"\n✅ Heavy AI dependencies: {ai_working}/{len(ai_deps)} working")
    
    # Summary
    total_tested = len(basic_deps) + len(data_deps) + len(ml_deps) + len(telegram_deps) + len(ai_deps)
    total_working = basic_working + data_working + ml_working + telegram_working + ai_working
    
    print("\n" + "=" * 50)
    print("🎯 DEPENDENCY ASSESSMENT SUMMARY:")
    print(f"📊 Total dependencies tested: {total_tested}")
    print(f"✅ Working dependencies: {total_working}")
    print(f"❌ Missing dependencies: {total_tested - total_working}")
    print(f"📈 Success rate: {(total_working/total_tested)*100:.1f}%")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    if basic_working == len(basic_deps):
        print("✅ Basic system can work with minimal dependencies")
    
    if data_working > 0:
        print("✅ Data processing capabilities available")
    else:
        print("⚠️ Need to install pandas/numpy for advanced features")
    
    if ml_working > 0:
        print("✅ Machine learning capabilities available")
    else:
        print("⚠️ Need to install scikit-learn for ML features")
    
    if telegram_working > 0:
        print("✅ Telegram bot capabilities available")
    else:
        print("⚠️ Need to install python-telegram-bot for bot features")
    
    if ai_working == 0:
        print("⚠️ Heavy AI dependencies not available - use simple alternatives")

if __name__ == "__main__":
    main()
