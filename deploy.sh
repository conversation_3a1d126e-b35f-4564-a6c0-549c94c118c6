#!/bin/bash
# EduGuideBot 2025 - One-Liner Deploy Script
# For live demos and presentations

set -e  # Exit on any error

echo "🚀 EDUGUIDEBOT 2025 - DEPLOY SCRIPT"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check prerequisites
print_info "Checking prerequisites..."

# Check if .env exists
if [ ! -f ".env" ]; then
    print_error ".env file not found!"
    print_info "Creating .env from template..."
    cp .env.example .env
    print_warning "Please edit .env file with your BOT_TOKEN before continuing"
    exit 1
fi

# Check if BOT_TOKEN is set
if ! grep -q "BOT_TOKEN=" .env || grep -q "BOT_TOKEN=your_token_here" .env; then
    print_error "BOT_TOKEN not properly set in .env file"
    print_info "Please edit .env file with your actual bot token"
    exit 1
fi

print_status "Prerequisites check passed"

# Build assets
print_info "Building optimized assets..."
make clean
make assets
print_status "Assets built successfully"

# Run quick tests
print_info "Running deployment validation..."
make test-quick
print_status "Validation passed"

# Choose deployment method
echo ""
echo "🎯 Choose deployment method:"
echo "1. Local development (recommended for demos)"
echo "2. Docker container"
echo "3. CLI demo only"
echo ""
read -p "Enter choice (1-3): " choice

case $choice in
    1)
        print_info "Starting local development server..."
        print_status "Bot will start with hot-reload enabled"
        print_info "Press Ctrl+C to stop"
        echo ""
        make dev
        ;;
    2)
        print_info "Building Docker image..."
        
        # Create Dockerfile if it doesn't exist
        if [ ! -f "Dockerfile" ]; then
            cat > Dockerfile << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# Install dependencies
COPY requirements_master.txt .
RUN pip install --no-cache-dir -r requirements_master.txt

# Copy application
COPY src/ src/
COPY data/ data/
COPY build/ build/
COPY .env .env

# Set environment
ENV PYTHONPATH=/app/src

# Run bot
CMD ["python", "src/bot/app.py"]
EOF
            print_status "Dockerfile created"
        fi
        
        # Build image
        docker build -t eduguidebot:latest .
        print_status "Docker image built"
        
        # Run container
        print_info "Starting Docker container..."
        docker run --rm -it \
            --name eduguidebot \
            -v "$(pwd)/.env:/app/.env:ro" \
            eduguidebot:latest
        ;;
    3)
        print_info "Starting CLI demo..."
        print_status "Perfect for offline presentations"
        echo ""
        python demo_cli.py
        ;;
    *)
        print_error "Invalid choice"
        exit 1
        ;;
esac

print_status "Deployment completed successfully!"
echo ""
print_info "🎓 Ready for university presentation!"
print_info "📱 Bot is running and ready for demo"
print_info "🎭 CLI demo available as backup: python demo_cli.py"
