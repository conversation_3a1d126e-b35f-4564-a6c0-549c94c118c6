#!/usr/bin/env python3
"""
QUICK TEST BOT - FOR IMMEDIATE TESTING
================================================================
Simple working Telegram bot for immediate testing
"""

import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Bot token
BOT_TOKEN = "8198268528:AAHuAvp3bRqO5hdPD3tbv1xSks2YjXtiu9Y"

# Simple user sessions
user_sessions = {}

# Simple questions
QUESTIONS = {
    1: {
        "question": "អ្នកចង់សិក្សាផ្នែកណា?",
        "options": [
            {"text": "វិទ្យាសាស្ត្រ", "value": "science"},
            {"text": "អាជីវកម្ម", "value": "business"},
            {"text": "សិល្បៈ", "value": "arts"},
            {"text": "បច្ចេកវិទ្យា", "value": "tech"}
        ]
    },
    2: {
        "question": "ថវិកាសិក្សារបស់អ្នក?",
        "options": [
            {"text": "ទាប (< $500)", "value": "low"},
            {"text": "មធ្យម ($500-1500)", "value": "medium"},
            {"text": "ខ្ពស់ (> $1500)", "value": "high"}
        ]
    }
}

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Start command handler."""
    user_id = update.effective_user.id
    user_sessions[user_id] = {"current_question": 1, "answers": {}}
    
    welcome_text = """🎓 សូមស្វាគមន៍មកកាន់ EduGuideBot 2025!

🤖 ប្រព័ន្ធណែនាំសាកលវិទ្យាល័យ
📊 ការវិភាគដោយប្រើ MCDA
🎯 ការណែនាំដោយផ្អែកលើទិន្នន័យពិត

ចាប់ផ្តើមការវាយតម្លៃ:"""
    
    await ask_question(update, context, 1)

async def ask_question(update: Update, context: ContextTypes.DEFAULT_TYPE, question_num: int) -> None:
    """Ask a question with inline keyboard."""
    if question_num not in QUESTIONS:
        await show_recommendations(update, context)
        return
    
    question = QUESTIONS[question_num]
    
    # Create inline keyboard
    keyboard = []
    for option in question["options"]:
        keyboard.append([InlineKeyboardButton(option["text"], callback_data=f"q{question_num}_{option['value']}")])
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    text = f"📋 សំណួរ {question_num}/2\n\n❓ {question['question']}"
    
    if update.callback_query:
        await update.callback_query.edit_message_text(text, reply_markup=reply_markup)
    else:
        await update.message.reply_text(text, reply_markup=reply_markup)

async def button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle button callbacks."""
    query = update.callback_query
    await query.answer()
    
    user_id = update.effective_user.id
    data = query.data
    
    if user_id not in user_sessions:
        user_sessions[user_id] = {"current_question": 1, "answers": {}}
    
    # Parse callback data
    if data.startswith("q"):
        parts = data.split("_")
        question_num = int(parts[0][1:])
        answer = parts[1]
        
        # Store answer
        user_sessions[user_id]["answers"][question_num] = answer
        
        # Move to next question
        next_question = question_num + 1
        user_sessions[user_id]["current_question"] = next_question
        
        await ask_question(update, context, next_question)

async def show_recommendations(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show final recommendations."""
    user_id = update.effective_user.id
    answers = user_sessions.get(user_id, {}).get("answers", {})
    
    # Simple recommendation logic
    field = answers.get(1, "science")
    budget = answers.get(2, "medium")
    
    recommendations = get_simple_recommendations(field, budget)
    
    result_text = "🎯 ការណែនាំសម្រាប់អ្នក:\n\n"
    
    for i, rec in enumerate(recommendations, 1):
        result_text += f"{i}. 🏫 {rec['university']}\n"
        result_text += f"   📚 {rec['major']}\n"
        result_text += f"   💰 ${rec['tuition']}/ឆ្នាំ\n"
        result_text += f"   🎯 ពិន្ទុ: {rec['score']:.1f}/10\n\n"
    
    result_text += "✅ ការវាយតម្លៃបានបញ្ចប់!\n"
    result_text += "🔄 ចុច /start ដើម្បីចាប់ផ្តើមឡើងវិញ"
    
    if update.callback_query:
        await update.callback_query.edit_message_text(result_text)
    else:
        await update.message.reply_text(result_text)

def get_simple_recommendations(field, budget):
    """Generate simple recommendations."""
    # Simple hardcoded recommendations for demo
    recommendations = {
        "science": [
            {"university": "វិទ្យាស្ថានបច្ចេកវិទ្យាកម្ពុជា", "major": "វិស្វកម្មកុំព្យូទ័រ", "tuition": 1200, "score": 9.2},
            {"university": "សាកលវិទ្យាល័យភូមិន្ទភ្នំពេញ", "major": "វិទ្យាសាស្ត្រកុំព្យូទ័រ", "tuition": 800, "score": 8.8},
            {"university": "សាកលវិទ្យាល័យអន្តរជាតិ", "major": "វិស្វកម្មសុីវិល", "tuition": 1500, "score": 8.5}
        ],
        "business": [
            {"university": "សាកលវិទ្យាល័យភូមិន្ទភ្នំពេញ", "major": "គ្រប់គ្រងអាជីវកម្ម", "tuition": 600, "score": 9.0},
            {"university": "សាកលវិទ្យាល័យអាមេរិកាំង", "major": "ហិរញ្ញវត្ថុ", "tuition": 1800, "score": 8.7},
            {"university": "សាកលវិទ្យាល័យអន្តរជាតិ", "major": "ទីផ្សារ", "tuition": 1200, "score": 8.3}
        ],
        "arts": [
            {"university": "សាកលវិទ្យាល័យភូមិន្ទភ្នំពេញ", "major": "សិល្បៈ", "tuition": 500, "score": 8.5},
            {"university": "សាកលវិទ្យាល័យអន្តរជាតិ", "major": "ការរចនា", "tuition": 1000, "score": 8.2},
            {"university": "វិទ្យាល័យសិល្បៈ", "major": "មេឌៀ", "tuition": 800, "score": 7.9}
        ],
        "tech": [
            {"university": "វិទ្យាស្ថានបច្ចេកវិទ្យាកម្ពុជា", "major": "បច្ចេកវិទ្យាព័ត៌មាន", "tuition": 1100, "score": 9.5},
            {"university": "សាកលវិទ្យាល័យភូមិន្ទភ្នំពេញ", "major": "វិស្វកម្មកុំព្យូទ័រ", "tuition": 900, "score": 9.1},
            {"university": "សាកលវិទ្យាល័យអន្តរជាតិ", "major": "វិទ្យាសាស្ត្រទិន្នន័យ", "tuition": 1400, "score": 8.8}
        ]
    }
    
    return recommendations.get(field, recommendations["science"])

def main() -> None:
    """Run the bot."""
    print("🚀 Starting EduGuideBot for testing...")
    print("📱 Bot: @EduGuideBotKH")
    print("🔗 Link: https://t.me/EduGuideBotKH")
    print("⏳ Bot is starting...")
    
    # Create application
    application = Application.builder().token(BOT_TOKEN).build()
    
    # Add handlers
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CallbackQueryHandler(button_callback))
    
    print("✅ Bot is running and ready for testing!")
    print("👆 Click /start in Telegram to test")
    
    # Run the bot
    application.run_polling(drop_pending_updates=True)

if __name__ == '__main__':
    main()
