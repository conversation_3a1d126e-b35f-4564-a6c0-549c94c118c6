# EduGuideBot 2025 Configuration Template
# Copy this file to .env and fill in your actual values

# REQUIRED: Get your bot token from @BotFather on Telegram
BOT_TOKEN=your_bot_token_here

# Optional Configuration
DEBUG=True
LOG_LEVEL=INFO

# Performance Settings
MAX_USERS=10000
RESPONSE_TIMEOUT=30
ENABLE_ANALYTICS=True

# Database Settings (if using external database)
# DATABASE_URL=your_database_url_here

# Webhook Settings (if using webhooks instead of polling)
# WEBHOOK_URL=your_webhook_url_here
# WEBHOOK_PORT=8443
