#!/usr/bin/env python3
"""
Smoke Tests - Critical Import Validation
================================================================

HONEST TESTING:
- Tests that core components can be imported
- Validates basic functionality
- No complex assertions - just "does it work?"

MUST PASS FOR GREEN CI
"""

import pytest
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_mcda_engine_import():
    """Test that MCDA engine can be imported."""
    try:
        from core.mcda import MCDAEngine, Program, Recommendation
        assert MCDAEngine is not None
        assert Program is not None
        assert Recommendation is not None
    except ImportError as e:
        pytest.fail(f"Failed to import MCDA engine: {e}")

def test_mcda_engine_initialization():
    """Test that MCDA engine can be initialized."""
    try:
        from core.mcda import MCDAEngine
        engine = MCDAEngine()
        assert engine is not None
        assert hasattr(engine, 'is_ready')
    except Exception as e:
        pytest.fail(f"Failed to initialize MCDA engine: {e}")

def test_telegram_bot_import():
    """Test that Telegram bot can be imported."""
    try:
        from bot.telegram_app import EduGuideBotApp
        assert EduGuideBotApp is not None
    except ImportError as e:
        pytest.fail(f"Failed to import Telegram bot: {e}")

def test_cli_import():
    """Test that CLI can be imported."""
    try:
        from bot.cli import CLIDemo
        assert CLIDemo is not None
    except ImportError as e:
        pytest.fail(f"Failed to import CLI: {e}")

def test_basic_recommendation_flow():
    """Test basic recommendation generation."""
    try:
        from core.mcda import MCDAEngine

        engine = MCDAEngine()

        test_answers = {
            'location': 'pp',
            'field': 'stem',
            'budget': 'mid'
        }

        recommendations = engine.get_recommendations(test_answers, limit=3)

        # Should return a list (even if empty)
        assert isinstance(recommendations, list)

    except Exception as e:
        pytest.fail(f"Basic recommendation flow failed: {e}")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
