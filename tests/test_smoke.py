#!/usr/bin/env python3
"""
Smoke Test - Critical Import Validation
================================================================

Tests that the bot can be imported and instantiated without crashes.
This is the canary test for memory/import issues.
"""

import pytest
import sys
import os
import tracemalloc
from pathlib import Path

def test_bot_imports():
    """Test that bot imports without memory issues."""
    # Start memory tracking
    tracemalloc.start()
    
    # Add src to path
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root / "src"))
    
    try:
        # Test critical imports
        from bot.app import EduGuideBotApp
        assert callable(EduGuideBotApp), "EduGuideBotApp should be callable"
        
        # Test instantiation (without running)
        app = EduGuideBotApp()
        assert app is not None, "Bot app should instantiate"
        
        # Check memory usage
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        # Memory should be reasonable (< 50MB for imports)
        peak_mb = peak / 1024 / 1024
        assert peak_mb < 50, f"Import memory usage too high: {peak_mb:.1f}MB"
        
        print(f"✅ Bot imports successfully, peak memory: {peak_mb:.1f}MB")
        
    except ImportError as e:
        pytest.fail(f"Import failed: {e}")
    except Exception as e:
        pytest.fail(f"Unexpected error during import: {e}")

def test_mcda_engine_imports():
    """Test that MCDA engine imports without issues."""
    tracemalloc.start()
    
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root / "src"))
    
    try:
        from mcda_recommender import MCDARecommendationEngine
        assert callable(MCDARecommendationEngine), "MCDA engine should be callable"
        
        # Test instantiation
        engine = MCDARecommendationEngine()
        assert engine is not None, "MCDA engine should instantiate"
        
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        peak_mb = peak / 1024 / 1024
        print(f"✅ MCDA engine imports successfully, peak memory: {peak_mb:.1f}MB")
        
    except Exception as e:
        pytest.fail(f"MCDA engine import failed: {e}")

def test_telegram_imports():
    """Test that Telegram libraries import without issues."""
    try:
        import telegram
        from telegram.ext import Application
        print("✅ Telegram libraries import successfully")
    except ImportError as e:
        pytest.skip(f"Telegram libraries not available: {e}")
    except Exception as e:
        pytest.fail(f"Telegram import error: {e}")

if __name__ == "__main__":
    # Run smoke tests directly
    test_bot_imports()
    test_mcda_engine_imports()
    test_telegram_imports()
    print("🎉 All smoke tests passed!")
