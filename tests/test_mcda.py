#!/usr/bin/env python3
"""
MCDA Recommendation Engine Tests
================================================================

Tests the MCDA engine against edge case personas to ensure:
- Recommendations are logical and consistent
- Edge cases are handled gracefully
- No crashes or errors occur
- Minimum recommendation counts are met
- Exclusion rules are respected
"""

import pytest
import yaml
import sys
import os
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from mcda_recommender import MCDARecommendationEngine, Recommendation

class TestMCDAEngine:
    """Test suite for MCDA Recommendation Engine."""
    
    @classmethod
    def setup_class(cls):
        """Set up test environment."""
        cls.engine = MCDARecommendationEngine()
        cls.test_personas = cls._load_test_personas()
    
    @classmethod
    def _load_test_personas(cls):
        """Load test personas from YAML file."""
        personas_file = Path(__file__).parent.parent / "data" / "edge_personas.yaml"
        
        try:
            with open(personas_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            return data.get('test_personas', [])
        except Exception as e:
            pytest.fail(f"Failed to load test personas: {e}")
    
    def test_engine_initialization(self):
        """Test that MCDA engine initializes correctly."""
        assert self.engine.is_ready, "MCDA engine should be ready after initialization"
        assert len(self.engine.programs) > 0, "Should have loaded programs from database"
        assert len(self.engine.weights_matrix) > 0, "Should have loaded weight matrix"
    
    def test_basic_recommendation_generation(self):
        """Test basic recommendation generation functionality."""
        # Simple test answers
        test_answers = {
            'location': 'phnom_penh',
            'budget': 'mid',
            'learning_mode': 'traditional',
            'interest_field': 'stem'
        }
        
        recommendations = self.engine.get_recommendations(test_answers, limit=5)
        
        assert len(recommendations) > 0, "Should generate at least one recommendation"
        assert all(isinstance(r, Recommendation) for r in recommendations), "All results should be Recommendation objects"
        assert all(r.total_score >= 0 for r in recommendations), "All scores should be non-negative"
        assert all(0 <= r.match_percentage <= 100 for r in recommendations), "Match percentages should be 0-100%"
    
    def test_recommendations_are_sorted(self):
        """Test that recommendations are returned in descending score order."""
        test_answers = {
            'location': 'phnom_penh',
            'budget': 'high',
            'interest_field': 'business'
        }
        
        recommendations = self.engine.get_recommendations(test_answers, limit=10)
        
        if len(recommendations) > 1:
            scores = [r.total_score for r in recommendations]
            assert scores == sorted(scores, reverse=True), "Recommendations should be sorted by score (descending)"
    
    def test_edge_case_personas(self):
        """Test edge case personas from the test suite."""
        personas = self._load_test_personas()

        for persona in personas:
            if not persona:
                continue

            persona_id = persona.get('id', 'unknown')
            answers = persona.get('answers', {})

            # Generate recommendations
            recommendations = self.engine.get_recommendations(answers, limit=10)

            # Test minimum recommendations requirement
            min_recs = persona.get('min_recommendations', 1)
            assert len(recommendations) >= min_recs, f"Persona {persona_id} should get at least {min_recs} recommendations"

            # Test expected field matching
            if 'expected_top_field' in persona:
                expected_field = persona['expected_top_field']
                top_rec = recommendations[0] if recommendations else None
                assert top_rec is not None, f"Persona {persona_id} should get at least one recommendation"
                assert top_rec.program.field_tag == expected_field, f"Top recommendation should be in {expected_field} field"
    
    def test_extreme_low_budget_it_online(self):
        """Specific test for low budget IT student wanting online learning."""
        answers = {
            'location': 'online',
            'budget': 'low',
            'learning_mode': 'online_only',
            'interest_field': 'stem',
            'career_goal': 'digital_tech',
            'scholarship': 'need'
        }
        
        recommendations = self.engine.get_recommendations(answers, limit=5)
        
        assert len(recommendations) >= 3, "Should get at least 3 recommendations"
        
        # Check that no high-tuition programs are recommended
        for rec in recommendations:
            assert rec.program.tuition_bracket != 'high', "Should not recommend high-tuition programs for low budget"
        
        # Check that STEM field is prioritized
        top_rec = recommendations[0]
        assert top_rec.program.field_tag == 'STEM', "Top recommendation should be STEM field"
    
    def test_premium_business_on_campus(self):
        """Specific test for high budget business student preferring campus."""
        answers = {
            'location': 'phnom_penh',
            'budget': 'high',
            'learning_mode': 'traditional',
            'interest_field': 'business',
            'career_goal': 'finance'
        }
        
        recommendations = self.engine.get_recommendations(answers, limit=5)
        
        assert len(recommendations) >= 3, "Should get at least 3 recommendations"
        
        # Check location preference
        top_rec = recommendations[0]
        assert top_rec.program.campus_city == 'PP', "Should prioritize Phnom Penh location"
        
        # Check field preference
        assert top_rec.program.field_tag == 'BUS', "Should prioritize business field"
    
    def test_explanation_generation(self):
        """Test that explanations are generated in Khmer."""
        test_answers = {
            'location': 'phnom_penh',
            'budget': 'mid',
            'interest_field': 'stem',
            'scholarship': 'need'
        }
        
        recommendations = self.engine.get_recommendations(test_answers, limit=3)
        
        for rec in recommendations:
            assert rec.explanation_kh, "Each recommendation should have Khmer explanation"
            assert isinstance(rec.explanation_kh, str), "Explanation should be string"
            assert len(rec.explanation_kh) > 0, "Explanation should not be empty"
    
    def test_score_breakdown_completeness(self):
        """Test that score breakdown is provided for transparency."""
        test_answers = {
            'location': 'phnom_penh',
            'budget': 'high',
            'interest_field': 'business',
            'career_goal': 'finance'
        }
        
        recommendations = self.engine.get_recommendations(test_answers, limit=3)
        
        for rec in recommendations:
            assert isinstance(rec.score_breakdown, dict), "Score breakdown should be dictionary"
            assert len(rec.score_breakdown) > 0, "Score breakdown should not be empty"
            assert rec.total_score == sum(rec.score_breakdown.values()), "Total score should equal sum of breakdown"
    
    def test_empty_answers_handling(self):
        """Test handling of empty or minimal answers."""
        empty_answers = {}
        recommendations = self.engine.get_recommendations(empty_answers, limit=5)
        
        # Should still return some recommendations (based on defaults/ROI)
        assert len(recommendations) > 0, "Should handle empty answers gracefully"
    
    def test_invalid_answers_handling(self):
        """Test handling of invalid answer values."""
        invalid_answers = {
            'location': 'invalid_location',
            'budget': 'invalid_budget',
            'nonexistent_question': 'some_value'
        }

        # Should not crash and should return some recommendations
        recommendations = self.engine.get_recommendations(invalid_answers, limit=5)
        assert isinstance(recommendations, list), "Should return list even with invalid answers"

    def test_roi_calculation_integration(self):
        """Test that ROI calculations work with real data."""
        # Test that STEM fields get higher ROI than ARTS (based on our CSV data)
        stem_major = {
            'major_info': {
                'name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
                'name_en': 'Computer Science'
            }
        }

        arts_major = {
            'major_info': {
                'name_kh': 'សិល្បៈ',
                'name_en': 'Fine Arts'
            }
        }

        stem_roi = self.engine._calculate_roi_score(stem_major)
        arts_roi = self.engine._calculate_roi_score(arts_major)

        # STEM should have higher ROI than Arts based on our reference data
        assert stem_roi > arts_roi, f"STEM ROI ({stem_roi}) should be higher than Arts ROI ({arts_roi})"

        # Both should be reasonable values (0-5 range)
        assert 0 <= stem_roi <= 5, f"STEM ROI should be 0-5, got {stem_roi}"
        assert 0 <= arts_roi <= 5, f"Arts ROI should be 0-5, got {arts_roi}"

if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
