#!/usr/bin/env python3
"""
Property-Based Testing for MCDA Engine
================================================================

Uses Hypothesis to generate thousands of random persona combinations
and test invariant properties of the MCDA recommendation system.

This catches edge cases that manual test personas might miss.
"""

import pytest
import sys
from pathlib import Path
from typing import Dict, List

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from hypothesis import given, strategies as st, settings, example
    from hypothesis.strategies import composite
    HYPOTHESIS_AVAILABLE = True
except ImportError:
    HYPOTHESIS_AVAILABLE = False
    pytest.skip("Hypothesis not available", allow_module_level=True)

from mcda_recommender import MCDARecommendationEngine

# Question value strategies
location_strategy = st.sampled_from(['phnom_penh', 'siem_reap', 'battambang', 'online'])
budget_strategy = st.sampled_from(['low', 'mid', 'high'])
learning_mode_strategy = st.sampled_from(['traditional', 'blended', 'online_only'])
interest_field_strategy = st.sampled_from(['stem', 'arts', 'business', 'agro_env', 'health'])
career_goal_strategy = st.sampled_from(['digital_tech', 'engineering', 'finance', 'education', 'unsure'])
language_level_strategy = st.sampled_from(['none', 'medium', 'high'])
strength_profile_strategy = st.sampled_from(['logic_math', 'balanced', 'creative'])
need_internship_strategy = st.sampled_from(['yes', 'maybe', 'no'])
scholarship_strategy = st.sampled_from(['need', 'maybe', 'none'])
doc_support_strategy = st.sampled_from(['self_prepare', 'need_help'])
study_time_strategy = st.sampled_from(['daytime', 'evening', 'flexible'])
internet_access_strategy = st.sampled_from(['stable', 'mobile_only', 'weak'])
green_value_strategy = st.sampled_from(['high', 'medium', 'low'])
work_abroad_strategy = st.sampled_from(['local', 'asean', 'global'])
academic_performance_strategy = st.sampled_from(['grade_a', 'grade_b', 'grade_c', 'grade_d'])
work_style_strategy = st.sampled_from(['teamwork', 'individual', 'both'])

@composite
def persona_strategy(draw):
    """Generate random but valid persona answers with contradiction filtering."""
    location = draw(location_strategy)
    learning_mode = draw(learning_mode_strategy)
    internet_access = draw(internet_access_strategy)
    study_time = draw(study_time_strategy)

    # Filter impossible combinations using assume()
    from hypothesis import assume

    # Can't have online-only learning with weak internet
    assume(not (learning_mode == 'online_only' and internet_access == 'weak'))

    # Can't have online location with traditional learning mode
    assume(not (location == 'online' and learning_mode == 'traditional'))

    # Can't have evening study time with online-only (usually daytime)
    assume(not (learning_mode == 'online_only' and study_time == 'evening'))

    return {
        'location': location,
        'budget': draw(budget_strategy),
        'learning_mode': learning_mode,
        'interest_field': draw(interest_field_strategy),
        'career_goal': draw(career_goal_strategy),
        'language_level': draw(language_level_strategy),
        'strength_profile': draw(strength_profile_strategy),
        'need_internship': draw(need_internship_strategy),
        'scholarship': draw(scholarship_strategy),
        'doc_support': draw(doc_support_strategy),
        'study_time': study_time,
        'internet_access': internet_access,
        'green_value': draw(green_value_strategy),
        'work_abroad': draw(work_abroad_strategy),
        'academic_performance': draw(academic_performance_strategy),
        'work_style': draw(work_style_strategy)
    }

@composite
def partial_persona_strategy(draw):
    """Generate persona with some missing answers (realistic scenario)."""
    full_persona = draw(persona_strategy())
    
    # Randomly remove some answers (simulate incomplete assessment)
    keys_to_keep = draw(st.lists(
        st.sampled_from(list(full_persona.keys())), 
        min_size=3, 
        max_size=len(full_persona),
        unique=True
    ))
    
    return {key: full_persona[key] for key in keys_to_keep}

class TestMCDAProperties:
    """Property-based tests for MCDA engine invariants."""
    
    @classmethod
    def setup_class(cls):
        """Set up MCDA engine for testing."""
        cls.engine = MCDARecommendationEngine()
        assert cls.engine.is_ready, "MCDA engine must be ready for property testing"
    
    @given(persona=persona_strategy())
    @settings(max_examples=100, deadline=None)
    def test_recommendations_always_returned(self, persona):
        """Property: System should always return some recommendations for valid input."""
        recommendations = self.engine.get_recommendations(persona, limit=10)
        
        # Invariant: Always get at least one recommendation
        assert len(recommendations) >= 1, "Should always return at least one recommendation"
        
        # Invariant: Never return more than requested
        assert len(recommendations) <= 10, "Should not return more than limit"
    
    @given(persona=persona_strategy())
    @settings(max_examples=50, deadline=None)
    def test_scores_are_non_negative(self, persona):
        """Property: All recommendation scores should be non-negative."""
        recommendations = self.engine.get_recommendations(persona, limit=5)
        
        for rec in recommendations:
            # Invariant: Total score is non-negative
            assert rec.total_score >= 0, f"Total score should be non-negative, got {rec.total_score}"
            
            # Invariant: Match percentage is between 0-100
            assert 0 <= rec.match_percentage <= 100, f"Match percentage should be 0-100%, got {rec.match_percentage}"
            
            # Invariant: Score breakdown components are non-negative
            for category, score in rec.score_breakdown.items():
                assert score >= 0, f"Score component {category} should be non-negative, got {score}"
    
    @given(persona=persona_strategy())
    @settings(max_examples=50, deadline=None)
    def test_recommendations_are_sorted(self, persona):
        """Property: Recommendations should be sorted by score (descending)."""
        recommendations = self.engine.get_recommendations(persona, limit=5)
        
        if len(recommendations) > 1:
            scores = [rec.total_score for rec in recommendations]
            # Invariant: Scores are in descending order
            assert scores == sorted(scores, reverse=True), "Recommendations should be sorted by score"
    
    @given(persona=persona_strategy())
    @settings(max_examples=30, deadline=None)
    def test_explanations_exist(self, persona):
        """Property: All recommendations should have explanations."""
        recommendations = self.engine.get_recommendations(persona, limit=3)
        
        for rec in recommendations:
            # Invariant: Explanation exists and is non-empty
            assert rec.explanation_kh, "Explanation should exist"
            assert isinstance(rec.explanation_kh, str), "Explanation should be string"
            assert len(rec.explanation_kh.strip()) > 0, "Explanation should not be empty"
    
    @given(persona=partial_persona_strategy())
    @settings(max_examples=50, deadline=None)
    def test_handles_incomplete_answers(self, persona):
        """Property: System should handle incomplete answer sets gracefully."""
        # Should not crash with partial answers
        recommendations = self.engine.get_recommendations(persona, limit=5)
        
        # Invariant: Still returns recommendations even with missing answers
        assert len(recommendations) >= 1, "Should handle incomplete answers gracefully"
        
        # Invariant: All returned recommendations are valid
        for rec in recommendations:
            assert rec.total_score >= 0, "Scores should still be valid with partial answers"
            assert rec.explanation_kh, "Explanations should still exist with partial answers"
    
    @given(
        persona=persona_strategy(),
        limit=st.integers(min_value=1, max_value=20)
    )
    @settings(max_examples=30, deadline=None)
    def test_limit_parameter_respected(self, persona, limit):
        """Property: Limit parameter should be respected."""
        recommendations = self.engine.get_recommendations(persona, limit=limit)
        
        # Invariant: Never return more than limit
        assert len(recommendations) <= limit, f"Should not exceed limit {limit}"
        
        # Invariant: If we have enough programs, should return exactly limit
        if len(self.engine.programs) >= limit:
            # Note: This might not always be true if filtering is very restrictive
            # So we just check it doesn't exceed
            pass
    
    @given(persona=persona_strategy())
    @settings(max_examples=20, deadline=None)
    def test_budget_constraints_respected(self, persona):
        """Property: Budget constraints should be respected in recommendations."""
        if persona.get('budget') == 'low':
            recommendations = self.engine.get_recommendations(persona, limit=5)
            
            # Check that low budget users don't get too many high-cost recommendations
            high_cost_count = sum(1 for rec in recommendations 
                                if rec.program.tuition_bracket == 'high')
            
            # Invariant: Low budget should prefer low/mid cost options
            # (Allow some high-cost if they have scholarships, but not majority)
            assert high_cost_count <= len(recommendations) // 2, \
                "Low budget users should not get majority high-cost recommendations"
    
    @given(persona=persona_strategy())
    @settings(max_examples=20, deadline=None)
    def test_location_preferences_influence_results(self, persona):
        """Property: Location preferences should influence recommendations."""
        if persona.get('location') in ['phnom_penh', 'siem_reap', 'battambang']:
            recommendations = self.engine.get_recommendations(persona, limit=5)
            
            # Count recommendations matching preferred location
            preferred_city_map = {
                'phnom_penh': 'PP',
                'siem_reap': 'SR', 
                'battambang': 'BB'
            }
            preferred_city = preferred_city_map[persona['location']]
            
            matching_location_count = sum(1 for rec in recommendations 
                                        if rec.program.campus_city == preferred_city)
            
            # Invariant: At least some recommendations should match location preference
            # (Unless no programs available in that location)
            if any(p.campus_city == preferred_city for p in self.engine.programs):
                assert matching_location_count > 0, \
                    f"Should have some recommendations in preferred city {preferred_city}"
    
    @example(persona={'location': 'online', 'budget': 'low', 'interest_field': 'stem'})
    @given(persona=persona_strategy())
    @settings(max_examples=30, deadline=None)
    def test_no_crashes_on_any_input(self, persona):
        """Property: System should never crash on any valid input combination."""
        try:
            recommendations = self.engine.get_recommendations(persona, limit=5)
            
            # Invariant: Should always return a list
            assert isinstance(recommendations, list), "Should always return a list"
            
            # Invariant: All items should be Recommendation objects
            for rec in recommendations:
                assert hasattr(rec, 'program'), "Should have program attribute"
                assert hasattr(rec, 'total_score'), "Should have total_score attribute"
                assert hasattr(rec, 'explanation_kh'), "Should have explanation_kh attribute"
                
        except Exception as e:
            pytest.fail(f"System crashed on input {persona}: {e}")

def test_pickle_memory_usage():
    """Test that pickle files stay under memory limits."""
    from pathlib import Path

    build_dir = Path("build")
    if not build_dir.exists():
        pytest.skip("Build directory not found")

    # Check each pickle file size
    for pkl_file in build_dir.glob("*.pkl"):
        size_bytes = pkl_file.stat().st_size
        size_mb = size_bytes / (1024 * 1024)

        # 20MB limit per file
        assert size_mb < 20, f"{pkl_file.name} is {size_mb:.1f}MB, exceeds 20MB limit"

        # Log sizes for monitoring
        print(f"📦 {pkl_file.name}: {size_mb:.1f}MB")

    print("✅ All pickle files under memory limits")

if __name__ == "__main__":
    # Run property-based tests
    pytest.main([__file__, "-v", "--tb=short"])
