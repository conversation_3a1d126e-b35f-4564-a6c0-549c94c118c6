#!/usr/bin/env python3
"""
Score Spread Tests - Verify No Flat-Line Issues
================================================================

Tests that recommendation engines produce varied scores.
Prevents the flat-line scoring bug that plagued earlier versions.
"""

import pytest
import sys
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.mcda import MCDAEngine
from core.ml_ranker import MLRanker
from core.recommender import RecommenderService

class TestScoreSpread:
    """Test score variety across different recommendation methods."""
    
    def test_mcda_score_spread(self):
        """Test that MCDA produces varied scores."""
        engine = MCDAEngine()
        
        test_answers = {
            'location': 'pp',
            'field': 'stem',
            'budget': 'mid',
            'career': 'tech'
        }
        
        recommendations = engine.get_recommendations(test_answers, limit=10)
        
        # Should have recommendations
        assert len(recommendations) > 0, "Should generate recommendations"
        
        # Extract scores
        scores = [rec.total_score for rec in recommendations]
        
        # Check for score variety
        unique_scores = len(set(scores))
        score_range = max(scores) - min(scores)
        
        # Should have at least 3 different scores
        assert unique_scores >= 3, f"Too few unique scores: {unique_scores} (scores: {scores})"
        
        # Score range should be meaningful (> 0.1)
        assert score_range > 0.1, f"Score range too small: {score_range} (scores: {scores})"
        
        print(f"✅ MCDA score spread: {unique_scores} unique scores, range: {score_range:.3f}")
    
    def test_mcda_different_inputs_different_scores(self):
        """Test that different inputs produce different score patterns."""
        engine = MCDAEngine()
        
        # Test different user profiles
        profiles = [
            {'location': 'pp', 'field': 'stem', 'budget': 'low', 'career': 'tech'},
            {'location': 'sr', 'field': 'business', 'budget': 'high', 'career': 'mgt'},
            {'location': 'btb', 'field': 'arts', 'budget': 'mid', 'career': 'creative'}
        ]
        
        all_scores = []
        
        for profile in profiles:
            recommendations = engine.get_recommendations(profile, limit=5)
            scores = [rec.total_score for rec in recommendations]
            all_scores.extend(scores)
        
        # Should have variety across all profiles
        unique_scores = len(set(all_scores))
        assert unique_scores >= 5, f"Insufficient score variety across profiles: {unique_scores}"
        
        print(f"✅ MCDA profile variety: {unique_scores} unique scores across {len(profiles)} profiles")
    
    @pytest.mark.skipif(not Path("build/ml_model.joblib").exists(), 
                        reason="ML model not trained yet")
    def test_ml_score_spread(self):
        """Test that ML ranker produces varied scores."""
        ranker = MLRanker()
        
        if not ranker.is_ready:
            pytest.skip("ML model not ready")
        
        # Load test programs
        programs_file = Path("build/programs.parquet")
        if not programs_file.exists():
            pytest.skip("Programs parquet file not found")
        
        programs_df = pd.read_parquet(programs_file)
        
        test_answers = {
            'location': 'pp',
            'field': 'stem',
            'budget': 'mid',
            'career': 'tech'
        }
        
        # Get ML rankings
        ranked_df = ranker.rank_programs(programs_df, test_answers)
        
        # Check score spread
        scores = ranked_df['ml_score'].tolist()[:10]  # Top 10
        
        unique_scores = len(set(scores))
        score_range = max(scores) - min(scores)
        
        # ML should produce varied scores (relaxed thresholds for realistic ML behavior)
        assert unique_scores >= 3, f"ML scores too uniform: {unique_scores} unique scores"
        assert score_range > 0.02, f"ML score range too small: {score_range}"
        
        print(f"✅ ML score spread: {unique_scores} unique scores, range: {score_range:.3f}")
    
    def test_recommender_service_score_spread(self):
        """Test that unified recommender produces varied scores."""
        service = RecommenderService(use_ml=True)
        
        if not service.is_ready:
            pytest.skip("Recommender service not ready")
        
        test_answers = {
            'location': 'pp',
            'field': 'stem',
            'budget': 'mid',
            'career': 'tech'
        }
        
        recommendations = service.get_recommendations(test_answers, limit=8)
        
        # Should have recommendations
        assert len(recommendations) > 0, "Should generate recommendations"
        
        # Extract final scores
        final_scores = [rec.final_score for rec in recommendations]
        mcda_scores = [rec.mcda_score for rec in recommendations]
        
        # Check final score variety
        unique_final = len(set(final_scores))
        unique_mcda = len(set(mcda_scores))
        
        assert unique_final >= 3, f"Final scores too uniform: {unique_final}"
        assert unique_mcda >= 3, f"MCDA scores too uniform: {unique_mcda}"
        
        # Check that scores are reasonable (0-1 range)
        assert all(0 <= score <= 1 for score in final_scores), "Scores outside valid range"
        
        print(f"✅ Service score spread: {unique_final} unique final scores, {unique_mcda} unique MCDA scores")
    
    def test_score_consistency(self):
        """Test that same inputs produce consistent scores."""
        engine = MCDAEngine()
        
        test_answers = {
            'location': 'pp',
            'field': 'stem',
            'budget': 'mid',
            'career': 'tech'
        }
        
        # Run twice
        recs1 = engine.get_recommendations(test_answers, limit=5)
        recs2 = engine.get_recommendations(test_answers, limit=5)
        
        # Should get same results
        assert len(recs1) == len(recs2), "Inconsistent recommendation count"
        
        for r1, r2 in zip(recs1, recs2):
            assert abs(r1.total_score - r2.total_score) < 0.001, "Inconsistent scoring"
            assert r1.program.university_id == r2.program.university_id, "Inconsistent ranking"
        
        print("✅ Score consistency verified")
    
    def test_no_flat_line_bug(self):
        """Specific test for the flat-line bug that was fixed."""
        engine = MCDAEngine()
        
        # This specific combination used to produce flat-line scores
        problematic_answers = {
            'location': 'phnom_penh',  # Note: different format
            'budget': 'mid',
            'interest_field': 'stem',
            'career_goal': 'digital_tech'
        }
        
        # Convert to new format
        converted_answers = {
            'location': 'pp',
            'field': 'stem',
            'budget': 'mid',
            'career': 'tech'
        }
        
        recommendations = engine.get_recommendations(converted_answers, limit=10)
        
        if recommendations:
            scores = [rec.total_score for rec in recommendations]
            
            # The old bug would produce scores like [11.036, 11.036, 11.036, ...]
            # Check that we don't have this pattern
            score_std = pd.Series(scores).std()
            
            # Standard deviation should be > 0.01 (not flat-line)
            assert score_std > 0.01, f"Flat-line detected! Score std: {score_std}, scores: {scores}"
            
            print(f"✅ No flat-line bug: score std = {score_std:.3f}")

def test_programs_data_variety():
    """Test that we have sufficient program variety for testing."""
    # Check if we have programs data
    programs_file = Path("build/programs.parquet")
    
    if programs_file.exists():
        df = pd.read_parquet(programs_file)
        
        # Should have variety in key dimensions
        unique_cities = df['city'].nunique()
        unique_fields = df['field_tag'].nunique()
        unique_careers = df['career_cluster'].nunique()
        
        assert unique_cities >= 2, f"Need more city variety: {unique_cities}"
        assert unique_fields >= 3, f"Need more field variety: {unique_fields}"
        assert unique_careers >= 3, f"Need more career variety: {unique_careers}"
        
        print(f"✅ Data variety: {unique_cities} cities, {unique_fields} fields, {unique_careers} careers")
    else:
        # Use MCDA sample data
        engine = MCDAEngine()
        programs = engine.programs
        
        assert len(programs) >= 2, "Need at least 2 programs for testing"
        
        cities = set(p.city for p in programs)
        fields = set(p.field_tag for p in programs)
        
        assert len(cities) >= 1, "Need city variety"
        assert len(fields) >= 2, "Need field variety"
        
        print(f"✅ Sample data variety: {len(cities)} cities, {len(fields)} fields")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
