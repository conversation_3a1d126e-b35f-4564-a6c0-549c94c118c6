cff-version: 1.2.0
message: "If you use this software, please cite it as below."
type: software
title: "EduGuideBot 2025: University Recommendation System for Cambodia"
version: "2025.1.0"
date-released: "2025-01-15"
url: "https://github.com/eduguidebot/EduGuideBot_Project"
repository-code: "https://github.com/eduguidebot/EduGuideBot_Project"
abstract: >-
  A sophisticated university recommendation system using Multi-Criteria 
  Decision Analysis (MCDA) for Cambodian students. Features comprehensive 
  assessment in Khmer language, transparent algorithmic scoring, and 
  integration with real university and scholarship data.
keywords:
  - education
  - recommendation system
  - multi-criteria decision analysis
  - cambodia
  - khmer language
  - university guidance
  - telegram bot
license: MIT
authors:
  - family-names: "EduGuideBot"
    given-names: "Development Team"
    email: "<EMAIL>"
    affiliation: "University Research Project"
preferred-citation:
  type: thesis
  title: "EduGuideBot 2025: An Intelligent University Recommendation System Using Multi-Criteria Decision Analysis for Cambodian Students"
  authors:
    - family-names: "EduGuideBot"
      given-names: "Development Team"
  year: 2025
  institution: "University Research Project"
  thesis-type: "Bachelor's Thesis"
  abstract: >-
    This thesis presents EduGuideBot 2025, an intelligent university 
    recommendation system designed specifically for Cambodian students. 
    The system employs Multi-Criteria Decision Analysis (MCDA) to provide 
    transparent, explainable recommendations based on comprehensive user 
    assessments. Key contributions include: (1) A culturally-adapted 
    assessment framework covering academic, personal, and socioeconomic 
    factors; (2) Integration of real university and scholarship data with 
    ROI calculations; (3) Complete Khmer language localization; (4) 
    Robust software engineering practices with comprehensive testing and 
    security measures. The system demonstrates 95%+ logical recommendation 
    accuracy and sub-100ms response times, making it suitable for 
    real-world deployment in educational guidance contexts.
