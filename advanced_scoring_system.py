#!/usr/bin/env python3
"""
ADVANCED MATHEMATICAL SCORING SYSTEM
================================================================

This is an advanced mathematical scoring system that:
1. Loads actual university data from JSON files
2. Extracts meaningful features for statistical analysis
3. Uses RandomForest algorithm for sophisticated scoring
4. Provides data-driven recommendations based on user preferences

HONEST ABOUT CAPABILITIES - ADVANCED MATH, NOT AI
"""

import json
import os
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UniversityDataLoader:
    """Loads and processes real university data from JSON files."""
    
    def __init__(self, data_directory="eduguide_2025/Uni Data"):
        self.data_directory = data_directory
        self.universities = []
        self.majors = []
        
    def load_all_universities(self):
        """Load all university data from JSON files."""
        logger.info("Loading university data...")
        
        # Walk through all directories (PP, BTB, SR)
        for root, dirs, files in os.walk(self.data_directory):
            for file in files:
                if file.endswith('.json'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            self._process_university_data(data, file_path)
                    except Exception as e:
                        logger.error(f"Error loading {file_path}: {e}")
        
        logger.info(f"Loaded {len(self.universities)} universities with {len(self.majors)} majors")
        return self.universities, self.majors
    
    def _process_university_data(self, data, file_path):
        """Process individual university JSON data."""
        try:
            university = data.get('university', {})
            majors = data.get('majors', [])
            
            # Extract university info
            uni_info = {
                'id': university.get('id', ''),
                'name_kh': university.get('name_kh', ''),
                'name_en': university.get('name_en', ''),
                'type': university.get('type', ''),
                'founding_year': university.get('founding_year', 0),
                'location_city': university.get('location', {}).get('city', ''),
                'file_path': file_path
            }
            
            self.universities.append(uni_info)
            
            # Extract major info
            for major in majors:
                major_info = self._extract_major_features(major, uni_info)
                if major_info:
                    self.majors.append(major_info)
                    
        except Exception as e:
            logger.error(f"Error processing university data from {file_path}: {e}")
    
    def _extract_major_features(self, major, university):
        """Extract meaningful features from major data for ML."""
        try:
            major_info = major.get('major_info', {})
            practical_info = major.get('practical_information', {})
            career_prospects = major.get('career_prospects', {})
            academic_req = major.get('academic_requirements', {})
            
            # Extract numerical features
            tuition_usd = self._safe_float(practical_info.get('tuition_fees_usd', '0'))
            min_gpa = self._safe_float(academic_req.get('minimum_gpa', 0))
            
            # Extract employment statistics
            employment_stats = career_prospects.get('employment_statistics', {})
            employment_rate = self._extract_percentage(employment_stats.get('employment_rate', '0%'))
            starting_salary = self._extract_salary(employment_stats.get('average_starting_salary', '0'))
            
            # Extract categorical features
            degree_level = major_info.get('degree_level_en', '').lower()
            study_type = major_info.get('study_type', '').lower()
            
            # Extract text features for NLP
            major_name_kh = major_info.get('name_kh', '')
            major_name_en = major_info.get('name_en', '')
            career_list_kh = ' '.join(career_prospects.get('potential_careers_kh', []))
            career_list_en = ' '.join(career_prospects.get('potential_careers_en', []))
            
            return {
                # University features
                'university_id': university['id'],
                'university_name': university['name_en'],
                'university_type': university['type'],
                'university_city': university['location_city'],
                'founding_year': university['founding_year'],
                
                # Major features
                'major_id': major_info.get('id', ''),
                'major_name_kh': major_name_kh,
                'major_name_en': major_name_en,
                'degree_level': degree_level,
                'study_type': study_type,
                
                # Financial features
                'tuition_usd': tuition_usd,
                'scholarship_available': practical_info.get('scholarship_availability', False),
                
                # Academic features
                'min_gpa': min_gpa,
                'study_duration': self._extract_duration(major_info.get('study_duration_kh', '')),
                
                # Career features
                'employment_rate': employment_rate,
                'starting_salary_usd': starting_salary,
                'career_keywords_kh': career_list_kh,
                'career_keywords_en': career_list_en,
                
                # Faculty features
                'faculty_name': major.get('faculty', {}).get('name_en', ''),
                'faculty_id': major.get('faculty', {}).get('id', '')
            }
            
        except Exception as e:
            logger.error(f"Error extracting major features: {e}")
            return None
    
    def _safe_float(self, value):
        """Safely convert string to float."""
        try:
            if isinstance(value, str):
                # Remove commas and convert
                value = value.replace(',', '')
            return float(value)
        except:
            return 0.0
    
    def _extract_percentage(self, percentage_str):
        """Extract percentage as float (e.g., '90%' -> 0.9)."""
        try:
            if isinstance(percentage_str, str) and '%' in percentage_str:
                return float(percentage_str.replace('%', '')) / 100.0
            return 0.0
        except:
            return 0.0
    
    def _extract_salary(self, salary_str):
        """Extract salary as USD float."""
        try:
            if isinstance(salary_str, str):
                # Remove 'USD' and other text, extract numbers
                import re
                numbers = re.findall(r'\d+', salary_str)
                if numbers:
                    return float(numbers[0])
            return 0.0
        except:
            return 0.0
    
    def _extract_duration(self, duration_str):
        """Extract study duration in years."""
        try:
            if isinstance(duration_str, str):
                import re
                numbers = re.findall(r'\d+', duration_str)
                if numbers:
                    return float(numbers[0])
            return 4.0  # Default to 4 years
        except:
            return 4.0

class AdvancedScoringEngine:
    """Advanced mathematical scoring engine using RandomForest algorithm."""
    
    def __init__(self):
        self.data_loader = UniversityDataLoader()
        self.df_majors = None
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.tfidf_vectorizer = TfidfVectorizer(max_features=100, stop_words='english')
        self.is_trained = False
        
    def load_and_prepare_data(self):
        """Load university data and prepare for ML training."""
        logger.info("Loading and preparing data for ML training...")
        
        universities, majors = self.data_loader.load_all_universities()
        
        if not majors:
            raise ValueError("No major data loaded!")
        
        # Convert to DataFrame
        self.df_majors = pd.DataFrame(majors)
        
        # Clean and prepare data
        self._clean_data()
        self._encode_categorical_features()
        self._prepare_text_features()
        
        logger.info(f"Prepared {len(self.df_majors)} majors for ML training")
        return self.df_majors
    
    def _clean_data(self):
        """Clean and validate the data."""
        # Remove rows with missing critical data
        self.df_majors = self.df_majors.dropna(subset=['major_name_en', 'tuition_usd'])
        
        # Fill missing values
        self.df_majors['employment_rate'] = self.df_majors['employment_rate'].fillna(0.0)
        self.df_majors['starting_salary_usd'] = self.df_majors['starting_salary_usd'].fillna(0.0)
        self.df_majors['min_gpa'] = self.df_majors['min_gpa'].fillna(2.5)
        
        logger.info(f"Cleaned data: {len(self.df_majors)} valid majors")
    
    def _encode_categorical_features(self):
        """Encode categorical features for ML."""
        categorical_features = ['university_type', 'university_city', 'degree_level', 
                              'study_type', 'faculty_name']
        
        for feature in categorical_features:
            if feature in self.df_majors.columns:
                le = LabelEncoder()
                self.df_majors[f'{feature}_encoded'] = le.fit_transform(
                    self.df_majors[feature].fillna('unknown')
                )
                self.label_encoders[feature] = le
    
    def _prepare_text_features(self):
        """Prepare text features using TF-IDF."""
        # Combine career keywords for TF-IDF
        career_text = (self.df_majors['career_keywords_en'].fillna('') + ' ' + 
                      self.df_majors['major_name_en'].fillna(''))
        
        # Fit TF-IDF vectorizer
        tfidf_matrix = self.tfidf_vectorizer.fit_transform(career_text)
        
        # Add TF-IDF features to dataframe
        tfidf_feature_names = [f'tfidf_{i}' for i in range(tfidf_matrix.shape[1])]
        tfidf_df = pd.DataFrame(tfidf_matrix.toarray(), columns=tfidf_feature_names)
        
        self.df_majors = pd.concat([self.df_majors.reset_index(drop=True), 
                                   tfidf_df.reset_index(drop=True)], axis=1)

    def train_recommendation_model(self):
        """Train REAL ML model for recommendations."""
        if self.df_majors is None:
            raise ValueError("Data not loaded! Call load_and_prepare_data() first.")

        logger.info("Training advanced mathematical scoring model...")

        # Define features for training
        feature_columns = [
            'tuition_usd', 'min_gpa', 'study_duration', 'employment_rate',
            'starting_salary_usd', 'founding_year',
            'university_type_encoded', 'university_city_encoded',
            'degree_level_encoded', 'study_type_encoded', 'faculty_name_encoded'
        ]

        # Add TF-IDF features
        tfidf_columns = [col for col in self.df_majors.columns if col.startswith('tfidf_')]
        feature_columns.extend(tfidf_columns)

        # Prepare features and target
        X = self.df_majors[feature_columns].fillna(0)

        # Create a composite score as target (employment_rate + normalized_salary - normalized_cost)
        normalized_salary = (self.df_majors['starting_salary_usd'] - self.df_majors['starting_salary_usd'].min()) / \
                           (self.df_majors['starting_salary_usd'].max() - self.df_majors['starting_salary_usd'].min() + 1e-8)
        normalized_cost = (self.df_majors['tuition_usd'] - self.df_majors['tuition_usd'].min()) / \
                         (self.df_majors['tuition_usd'].max() - self.df_majors['tuition_usd'].min() + 1e-8)

        y = self.df_majors['employment_rate'] + normalized_salary - (normalized_cost * 0.3)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)

        # Train Random Forest model
        self.model = RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10)
        self.model.fit(X_train_scaled, y_train)

        # Evaluate model
        y_pred = self.model.predict(X_test_scaled)
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        logger.info(f"Model trained! MSE: {mse:.4f}, R²: {r2:.4f}")

        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': feature_columns,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)

        logger.info("Top 10 most important features:")
        for _, row in feature_importance.head(10).iterrows():
            logger.info(f"  {row['feature']}: {row['importance']:.4f}")

        self.is_trained = True
        return mse, r2

    def get_recommendations(self, user_preferences):
        """Get REAL ML-powered recommendations based on user preferences."""
        if not self.is_trained:
            raise ValueError("Model not trained! Call train_recommendation_model() first.")

        logger.info("Generating ML-powered recommendations...")

        # Filter based on user constraints
        filtered_df = self.df_majors.copy()

        # Budget filter
        if 'max_budget_usd' in user_preferences:
            filtered_df = filtered_df[filtered_df['tuition_usd'] <= user_preferences['max_budget_usd']]

        # Location filter
        if 'preferred_city' in user_preferences:
            filtered_df = filtered_df[filtered_df['university_city'] == user_preferences['preferred_city']]

        # GPA filter
        if 'gpa' in user_preferences:
            filtered_df = filtered_df[filtered_df['min_gpa'] <= user_preferences['gpa']]

        if len(filtered_df) == 0:
            return []

        # Prepare features for prediction
        feature_columns = [
            'tuition_usd', 'min_gpa', 'study_duration', 'employment_rate',
            'starting_salary_usd', 'founding_year',
            'university_type_encoded', 'university_city_encoded',
            'degree_level_encoded', 'study_type_encoded', 'faculty_name_encoded'
        ]
        tfidf_columns = [col for col in filtered_df.columns if col.startswith('tfidf_')]
        feature_columns.extend(tfidf_columns)

        X = filtered_df[feature_columns].fillna(0)
        X_scaled = self.scaler.transform(X)

        # Get ML predictions
        predictions = self.model.predict(X_scaled)

        # Add predictions to dataframe
        filtered_df = filtered_df.copy()
        filtered_df['ml_score'] = predictions

        # Sort by ML score
        recommendations = filtered_df.sort_values('ml_score', ascending=False).head(10)

        # Format recommendations
        results = []
        for _, row in recommendations.iterrows():
            results.append({
                'university_name': row['university_name'],
                'major_name_kh': row['major_name_kh'],
                'major_name_en': row['major_name_en'],
                'tuition_usd': row['tuition_usd'],
                'employment_rate': row['employment_rate'],
                'starting_salary_usd': row['starting_salary_usd'],
                'university_city': row['university_city'],
                'ml_score': row['ml_score'],
                'faculty_name': row['faculty_name']
            })

        logger.info(f"Generated {len(results)} ML-powered recommendations")
        return results

if __name__ == "__main__":
    # Test the Advanced Mathematical Scoring System
    engine = AdvancedScoringEngine()

    # Load data
    df = engine.load_and_prepare_data()
    print(f"✅ Loaded {len(df)} majors for mathematical analysis!")

    # Train model
    mse, r2 = engine.train_recommendation_model()
    print(f"✅ Mathematical model trained! MSE: {mse:.4f}, R²: {r2:.4f}")

    # Test recommendations
    user_prefs = {
        'max_budget_usd': 1000,
        'preferred_city': 'ភ្នំពេញ',
        'gpa': 3.0
    }

    recommendations = engine.get_recommendations(user_prefs)
    print(f"\n✅ Generated {len(recommendations)} recommendations!")

    print("\nTop 3 mathematically-scored recommendations:")
    for i, rec in enumerate(recommendations[:3], 1):
        print(f"{i}. {rec['university_name']} - {rec['major_name_en']}")
        print(f"   Tuition: ${rec['tuition_usd']}, Employment: {rec['employment_rate']:.1%}")
        print(f"   Math Score: {rec['ml_score']:.3f}")
        print()
