name: Weekly Sanity Check

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:  # Allow manual trigger

jobs:
  sanity-check:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements_master.txt
        pip install -r requirements_dev.txt
    
    - name: Build assets and check memory
      env:
        BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
      run: |
        make clean
        make assets
        
        # Check pickle file sizes
        python -c "
        from pathlib import Path
        import sys
        
        build_dir = Path('build')
        total_size_mb = 0
        
        if build_dir.exists():
            for pkl_file in build_dir.glob('*.pkl'):
                size_mb = pkl_file.stat().st_size / (1024 * 1024)
                total_size_mb += size_mb
                print(f'📦 {pkl_file.name}: {size_mb:.1f} MB')
                
                if size_mb > 20:
                    print(f'❌ {pkl_file.name} exceeds 20MB limit!')
                    sys.exit(1)
            
            print(f'📊 Total build size: {total_size_mb:.1f} MB')
            
            if total_size_mb > 50:
                print('⚠️ Total build size exceeds 50MB - consider optimization')
            
            print('✅ All pickle files within memory limits')
        else:
            print('❌ Build directory not found')
            sys.exit(1)
        "
    
    - name: Run memory profile tests
      env:
        BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
      run: |
        python -m pytest tests/test_property_based.py::test_pickle_memory_usage -v
    
    - name: Run sanity check script
      env:
        BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
        ENVIRONMENT: testing
      run: |
        python sanity_check.py
    
    - name: Create issue on failure
      if: failure()
      uses: actions/github-script@v6
      with:
        script: |
          github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: '🚨 Weekly Sanity Check Failed',
            body: `
            The weekly sanity check has failed. Please investigate:
            
            - Check build artifact sizes
            - Verify memory limits are respected
            - Review recent changes that might affect performance
            
            **Workflow Run:** ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
            `,
            labels: ['bug', 'maintenance', 'high-priority']
          })
